package com.iotlaser.spms.common.utils;

import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 价税分离计算工具类
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Slf4j
public class TaxCalculationUtils {

    /**
     * 税率精度：小数点后4位
     */
    private static final int TAX_RATE_SCALE = 4;

    /**
     * 金额精度：小数点后2位
     */
    private static final int AMOUNT_SCALE = 2;

    /**
     * 单价精度：小数点后2位
     */
    private static final int PRICE_SCALE = 2;

    /**
     * 舍入模式：四舍五入
     */
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 最大税率：100%
     */
    private static final BigDecimal MAX_TAX_RATE = new BigDecimal("100");

    /**
     * 最小税率：0%
     */
    private static final BigDecimal MIN_TAX_RATE = BigDecimal.ZERO;

    /**
     * 百分比转换基数：100
     */
    private static final BigDecimal PERCENTAGE_BASE = new BigDecimal("100");

    /**
     * 智能计算价税分离字段（标准BO返回）
     * <p>
     * 推荐使用此方法，返回标准的业务对象
     *
     * @param quantity 数量
     * @param taxRate  税率 (%)
     * @param price    单价(含税) (可选)
     * @return 计算结果（标准BO对象）
     * @throws ServiceException 当参数无效或计算失败时
     */
    public static TaxCalculationResultBo calculate(BigDecimal quantity, BigDecimal taxRate, BigDecimal price) {
        return calculate(quantity, taxRate, price, null, null, null);
    }

    /**
     * 智能计算价税分离字段（标准BO返回）
     * <p>
     * 推荐使用此方法，返回标准的业务对象
     *
     * @param quantity           数量
     * @param taxRate            税率 (%)
     * @param price              单价(含税) (可选)
     * @param priceExclusiveTax  单价(不含税) (可选)
     * @param amount             金额(含税) (可选)
     * @param amountExclusiveTax 金额(不含税) (可选)
     * @return 计算结果（标准BO对象）
     * @throws ServiceException 当参数无效或计算失败时
     */
    public static TaxCalculationResultBo calculate(BigDecimal quantity, BigDecimal taxRate,
                                                   BigDecimal price, BigDecimal priceExclusiveTax,
                                                   BigDecimal amount, BigDecimal amountExclusiveTax) {
        log.debug("开始价税分离计算 - 数量: {}, 税率: {}%, 含税单价: {}, 不含税单价: {}, 含税金额: {}, 不含税金额: {}",
            quantity, taxRate, price, priceExclusiveTax, amount, amountExclusiveTax);

        try {
            // 校验必要字段
            validateInputs(quantity, taxRate);

            TaxCalculationResultBo result = new TaxCalculationResultBo();
            result.setQuantity(quantity);
            result.setTaxRate(taxRate);

            // 根据输入的字段计算其他字段
            if (priceExclusiveTax != null) {
                log.debug("基于单价(不含税)进行计算: {}", priceExclusiveTax);
                calculateFromPriceExclusiveTax(result, priceExclusiveTax);
            } else if (price != null) {
                log.debug("基于单价(含税)进行计算: {}", price);
                calculateFromPrice(result, price);
            } else if (amountExclusiveTax != null) {
                log.debug("基于金额(不含税)进行计算: {}", amountExclusiveTax);
                calculateFromAmountExclusiveTax(result, amountExclusiveTax);
            } else if (amount != null) {
                log.debug("基于金额(含税)进行计算: {}", amount);
                calculateFromTotalAmount(result, amount);
            } else {
                throw new ServiceException("必须提供单价或金额信息");
            }

            log.info("价税分离计算完成 - 不含税金额: {}, 税额: {}, 含税金额: {}",
                result.getAmountExclusiveTax(), result.getTaxAmount(), result.getAmount());

            return result;

        } catch (ServiceException e) {
            log.error("价税分离计算失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("价税分离计算异常: {}", e.getMessage(), e);
            throw new ServiceException("价税分离计算异常: " + e.getMessage());
        }
    }

    /**
     * 基于单价(不含税)计算其他字段
     */
    private static void calculateFromPriceExclusiveTax(TaxCalculationResultBo result, BigDecimal priceExclusiveTax) {
        result.setPriceExclusiveTax(priceExclusiveTax);

        // 计算金额(不含税) = 单价(不含税) × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(result.getQuantity())
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmountExclusiveTax(amountExclusiveTax);

        // 计算税额 = 金额(不含税) × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(
                result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE))
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setTaxAmount(taxAmount);

        // 计算单价(含税) = 单价(不含税) × (1 + 税率)
        BigDecimal price = priceExclusiveTax.multiply(
                BigDecimal.ONE.add(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)))
            .setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setPrice(price);

        // 计算金额(含税) = 金额(不含税) + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        result.setAmount(amount);
    }

    /**
     * 基于单价(含税)计算其他字段
     */
    private static void calculateFromPrice(TaxCalculationResultBo result, BigDecimal price) {
        result.setPrice(price);
        // 计算金额(含税) = 单价(含税) × 数量
        BigDecimal amount = price.multiply(result.getQuantity()).setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmount(amount);
        // 计算单价(不含税) = 单价(含税) ÷ (1 + 税率)
        BigDecimal priceExclusiveTax = price.divide(BigDecimal.ONE.add(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)),AMOUNT_SCALE, ROUNDING_MODE);
        result.setPriceExclusiveTax(priceExclusiveTax);
        // 计算金额(不含税) = 单价(不含税) × 数量
        BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(result.getQuantity()).setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmountExclusiveTax(amountExclusiveTax);
        // 计算税额 = 金额(含税) - 金额(不含税)
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        result.setTaxAmount(taxAmount);
    }

    /**
     * 基于金额(不含税)计算其他字段
     */
    private static void calculateFromAmountExclusiveTax(TaxCalculationResultBo result, BigDecimal amountExclusiveTax) {
        result.setAmountExclusiveTax(amountExclusiveTax);
        // 计算单价(不含税) = 金额(不含税) ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPriceExclusiveTax(priceExclusiveTax);
        // 计算税额 = 金额(不含税) × 税率
        BigDecimal taxAmount = amountExclusiveTax.multiply(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)).setScale(AMOUNT_SCALE, ROUNDING_MODE);
        result.setTaxAmount(taxAmount);
        // 计算金额(含税) = 金额(不含税) + 税额
        BigDecimal amount = amountExclusiveTax.add(taxAmount);
        result.setAmount(amount);
        // 计算单价(含税) = 金额(含税) ÷ 数量
        BigDecimal price = amount.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPrice(price);
    }

    /**
     * 基于金额(含税)计算其他字段
     */
    private static void calculateFromTotalAmount(TaxCalculationResultBo result, BigDecimal amount) {
        result.setAmount(amount);
        // 计算单价(含税) = 金额(含税) ÷ 数量
        BigDecimal price = amount.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPrice(price);
        // 计算金额(不含税) = 金额(含税) ÷ (1 + 税率)
        BigDecimal amountExclusiveTax = amount.divide(BigDecimal.ONE.add(result.getTaxRate().divide(new BigDecimal("100"), TAX_RATE_SCALE, ROUNDING_MODE)), AMOUNT_SCALE, ROUNDING_MODE);
        result.setAmountExclusiveTax(amountExclusiveTax);
        // 计算单价(不含税) = 金额(不含税) ÷ 数量
        BigDecimal priceExclusiveTax = amountExclusiveTax.divide(result.getQuantity(), AMOUNT_SCALE, ROUNDING_MODE);
        result.setPriceExclusiveTax(priceExclusiveTax);
        // 计算税额 = 金额(含税) - 金额(不含税)
        BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        result.setTaxAmount(taxAmount);
    }

    /**
     * 小数税率转换为百分比税率
     * <p>
     * 例如：0.13 → 13
     *
     * @param taxRate 小数税率（如 0.13 表示 13%）
     * @return 百分比税率（如 13 表示 13%）
     * @throws ServiceException 当税率无效时
     */
    public static BigDecimal decimalToPercentage(BigDecimal taxRate) {
        if (taxRate == null) {
            throw new ServiceException("税率不能为空");
        }
        // 校验小数税率范围 (0-1)
        if (taxRate.compareTo(BigDecimal.ZERO) < 0 || taxRate.compareTo(BigDecimal.ONE) > 0) {
            throw new ServiceException("小数税率必须在0-1之间，当前值: " + taxRate);
        }
        return taxRate.multiply(PERCENTAGE_BASE);
    }

    /**
     * 百分比税率转换为小数税率
     * <p>
     * 例如：13 → 0.13
     *
     * @param percentageTaxRate 百分比税率（如 13 表示 13%）
     * @return 小数税率（如 0.13 表示 13%）
     * @throws ServiceException 当税率无效时
     */
    public static BigDecimal percentageToDecimal(BigDecimal percentageTaxRate) {
        if (percentageTaxRate == null) {
            throw new ServiceException("税率不能为空");
        }

        // 校验百分比税率范围 (0-100)
        if (percentageTaxRate.compareTo(MIN_TAX_RATE) < 0 || percentageTaxRate.compareTo(MAX_TAX_RATE) > 0) {
            throw new ServiceException("百分比税率必须在0-100之间，当前值: " + percentageTaxRate);
        }

        return percentageTaxRate.divide(PERCENTAGE_BASE, TAX_RATE_SCALE, ROUNDING_MODE);
    }

    /**
     * 校验输入参数
     * <p>
     * 增强的参数校验，包含更详细的错误信息和边界检查
     *
     * @param quantity 数量
     * @param taxRate  税率（百分比格式）
     * @throws ServiceException 当参数无效时
     */
    private static void validateInputs(BigDecimal quantity, BigDecimal taxRate) {
        // 校验数量
        if (quantity == null) {
            throw new ServiceException("数量不能为空");
        }
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("数量必须大于0，当前值: " + quantity);
        }
        // 校验数量是否过大（防止计算溢出）
        if (quantity.compareTo(new BigDecimal("*********")) > 0) {
            throw new ServiceException("数量过大，可能导致计算溢出，当前值: " + quantity);
        }
        // 校验税率
        if (taxRate == null) {
            throw new ServiceException("税率不能为空");
        }
        if (taxRate.compareTo(MIN_TAX_RATE) < 0 || taxRate.compareTo(MAX_TAX_RATE) > 0) {
            throw new ServiceException("税率必须在0-100%之间，当前值: " + taxRate + "%");
        }
        log.debug("参数校验通过 - 数量: {}, 税率: {}%", quantity, taxRate);
    }

    /**
     * 计算税额
     * <p>
     * 公式：税额 = 不含税金额 × 税率
     *
     * @param amountExclusiveTax 金额(不含税)
     * @param taxRate            税率 (%)
     * @return 税额
     * @throws ServiceException 当计算失败时
     */
    public static BigDecimal calculateTaxAmount(BigDecimal amountExclusiveTax, BigDecimal taxRate) {
        log.debug("计算税额 - 不含税金额: {}, 税率: {}%", amountExclusiveTax, taxRate);
        if (amountExclusiveTax == null || taxRate == null) {
            log.warn("计算税额时参数为空，返回0 - 不含税金额: {}, 税率: {}", amountExclusiveTax, taxRate);
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal result = amountExclusiveTax.multiply(taxRate.divide(PERCENTAGE_BASE, TAX_RATE_SCALE, ROUNDING_MODE)).setScale(AMOUNT_SCALE, ROUNDING_MODE);
            log.debug("税额计算完成 - 结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("税额计算失败 - 不含税金额: {}, 税率: {}%, 错误: {}", amountExclusiveTax, taxRate, e.getMessage());
            throw new ServiceException("税额计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算金额(含税)
     *
     * @param amountExclusiveTax 金额(不含税)
     * @param taxAmount          税额
     * @return 金额(含税)
     */
    public static BigDecimal calculateTotalAmount(BigDecimal amountExclusiveTax, BigDecimal taxAmount) {
        if (amountExclusiveTax == null) {
            amountExclusiveTax = BigDecimal.ZERO;
        }
        if (taxAmount == null) {
            taxAmount = BigDecimal.ZERO;
        }
        return amountExclusiveTax.add(taxAmount);
    }

    /**
     * 计算金额(不含税)
     * <p>
     * 公式：不含税金额 = 含税金额 ÷ (1 + 税率)
     *
     * @param amount  金额(含税)
     * @param taxRate 税率 (%)
     * @return 金额(不含税)
     * @throws ServiceException 当计算失败时
     */
    public static BigDecimal calculateAmountExclusiveTax(BigDecimal amount, BigDecimal taxRate) {
        log.debug("计算不含税金额 - 含税金额: {}, 税率: {}%", amount, taxRate);

        if (amount == null || taxRate == null) {
            log.warn("计算不含税金额时参数为空，返回0 - 含税金额: {}, 税率: {}", amount, taxRate);
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(PERCENTAGE_BASE, TAX_RATE_SCALE, ROUNDING_MODE));
            BigDecimal result = amount.divide(divisor, AMOUNT_SCALE, ROUNDING_MODE);
            log.debug("不含税金额计算完成 - 结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("不含税金额计算失败 - 含税金额: {}, 税率: {}%, 错误: {}", amount, taxRate, e.getMessage());
            throw new ServiceException("不含税金额计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算含税单价
     * <p>
     * 公式：含税单价 = 不含税单价 × (1 + 税率)
     *
     * @param priceExclusiveTax 不含税单价
     * @param taxRate           税率 (%)
     * @return 含税单价
     * @throws ServiceException 当计算失败时
     */
    public static BigDecimal calculateInclusivePrice(BigDecimal priceExclusiveTax, BigDecimal taxRate) {
        log.debug("计算含税单价 - 不含税单价: {}, 税率: {}%", priceExclusiveTax, taxRate);

        if (priceExclusiveTax == null || taxRate == null) {
            log.warn("计算含税单价时参数为空，返回0 - 不含税单价: {}, 税率: {}", priceExclusiveTax, taxRate);
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal multiplier = BigDecimal.ONE.add(taxRate.divide(PERCENTAGE_BASE, TAX_RATE_SCALE, ROUNDING_MODE));
            BigDecimal result = priceExclusiveTax.multiply(multiplier).setScale(PRICE_SCALE, ROUNDING_MODE);

            log.debug("含税单价计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("含税单价计算失败 - 不含税单价: {}, 税率: {}%, 错误: {}", priceExclusiveTax, taxRate, e.getMessage());
            throw new ServiceException("含税单价计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算不含税单价
     * <p>
     * 公式：不含税单价 = 含税单价 ÷ (1 + 税率)
     *
     * @param inclusivePrice 含税单价
     * @param taxRate        税率 (%)
     * @return 不含税单价
     * @throws ServiceException 当计算失败时
     */
    public static BigDecimal calculateExclusivePrice(BigDecimal inclusivePrice, BigDecimal taxRate) {
        log.debug("计算不含税单价 - 含税单价: {}, 税率: {}%", inclusivePrice, taxRate);
        if (inclusivePrice == null || taxRate == null) {
            log.warn("计算不含税单价时参数为空，返回0 - 含税单价: {}, 税率: {}", inclusivePrice, taxRate);
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(PERCENTAGE_BASE, TAX_RATE_SCALE, ROUNDING_MODE));
            BigDecimal result = inclusivePrice.divide(divisor, PRICE_SCALE, ROUNDING_MODE);
            log.debug("不含税单价计算完成 - 结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("不含税单价计算失败 - 含税单价: {}, 税率: {}%, 错误: {}", inclusivePrice, taxRate, e.getMessage());
            throw new ServiceException("不含税单价计算失败: " + e.getMessage());
        }
    }

    /**
     * 验证价税分离计算结果的一致性
     * <p>
     * 检查计算结果是否符合价税分离的基本公式
     *
     * @param result 计算结果
     * @return 是否一致
     */
    public static boolean validateCalculationConsistency(TaxCalculationResultBo result) {
        if (result == null) {
            return false;
        }
        try {
            // 允许的误差范围（0.01）
            BigDecimal tolerance = new BigDecimal("0.01");

            // 验证：含税金额 = 不含税金额 + 税额
            if (result.getAmount() != null && result.getAmountExclusiveTax() != null && result.getTaxAmount() != null) {
                BigDecimal expectedAmount = result.getAmountExclusiveTax().add(result.getTaxAmount());
                if (isWithinTolerance(result.getAmount(), expectedAmount, tolerance)) {
                    log.warn("金额一致性验证失败 - 含税金额: {}, 期望值: {}", result.getAmount(), expectedAmount);
                    return false;
                }
            }
            // 验证：含税金额 = 含税单价 × 数量
            if (result.getAmount() != null && result.getPrice() != null && result.getQuantity() != null) {
                BigDecimal expectedAmount = result.getPrice().multiply(result.getQuantity()).setScale(AMOUNT_SCALE, ROUNDING_MODE);
                if (isWithinTolerance(result.getAmount(), expectedAmount, tolerance)) {
                    log.warn("单价数量一致性验证失败 - 含税金额: {}, 期望值: {}", result.getAmount(), expectedAmount);
                    return false;
                }
            }
            log.debug("价税分离计算结果一致性验证通过");
            return true;
        } catch (Exception e) {
            log.error("价税分离计算结果一致性验证异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查两个数值是否在容差范围内
     */
    private static boolean isWithinTolerance(BigDecimal actual, BigDecimal expected, BigDecimal tolerance) {
        if (actual == null && expected == null) {
            return false;
        }
        if (actual == null || expected == null) {
            return true;
        }
        BigDecimal difference = actual.subtract(expected).abs();
        return difference.compareTo(tolerance) > 0;
    }
}
