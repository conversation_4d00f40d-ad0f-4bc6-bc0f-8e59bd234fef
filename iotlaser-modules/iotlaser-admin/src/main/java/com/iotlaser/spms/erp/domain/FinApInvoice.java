package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 应付单对象 erp_fin_ap_invoice
 *
 * <AUTHOR> Kai
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ap_invoice")
public class FinApInvoice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应付ID
     */
    @TableId(value = "invoice_id")
    private Long invoiceId;

    /**
     * 应付编号
     */
    private String invoiceCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编号
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编号
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 收款方类型
     */
    private FinPayeeType payeeType;

    /**
     * 收款方ID
     */
    private Long payeeId;

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    private BigDecimal taxAmount;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 应付状态
     */
    private FinApInvoiceStatus invoiceStatus;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
