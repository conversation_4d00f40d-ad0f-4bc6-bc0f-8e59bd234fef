package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购入库对象 erp_purchase_inbound
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_inbound")
public class PurchaseInbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @TableId(value = "inbound_id")
    private Long inboundId;

    /**
     * 入库单编号
     */
    private String inboundCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 入库时间
     */
    private LocalDateTime inboundTime;

    /**
     * 入库状态
     */
    private PurchaseInboundStatus inboundStatus;

    /**
     * 收货负责人ID
     */
    private Long handlerId;

    /**
     * 收货负责人
     */
    private String handlerName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<PurchaseInboundItem> items;
}
