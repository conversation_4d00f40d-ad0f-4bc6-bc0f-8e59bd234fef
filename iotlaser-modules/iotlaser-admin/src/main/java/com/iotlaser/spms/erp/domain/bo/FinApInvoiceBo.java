package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinApInvoice;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 应付单业务对象 erp_fin_ap_invoice
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApInvoice.class, reverseConvertGenerate = false)
public class FinApInvoiceBo extends BaseEntity {

    /**
     * 应付ID
     */
    private Long invoiceId;

    /**
     * 应付编号
     */
    private String invoiceCode;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sourceId;

    /**
     * 源头编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 收款方类型
     */
    @NotBlank(message = "收款方类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private FinPayeeType payeeType;

    /**
     * 收款方ID
     */
    private Long payeeId;

    /**
     * 收款方名称
     */
    private String payeeName;


    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 总税额
     */
    private BigDecimal taxAmount;

    /**
     * 金额(含税)
     */
    @NotNull(message = "金额(含税)不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal amount;

    /**
     * 应付状态
     */
    @NotBlank(message = "应付状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private FinApInvoiceStatus invoiceStatus;

    /**
     * 摘要
     */
    @NotBlank(message = "摘要不能为空", groups = {AddGroup.class, EditGroup.class})
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
