package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinArReceiptOrder;
import com.iotlaser.spms.erp.enums.FinAccountType;
import com.iotlaser.spms.erp.enums.FinArReceiptStatus;
import com.iotlaser.spms.erp.enums.FinPaymentMethod;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收款单业务对象 erp_fin_ar_receipt_order
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinArReceiptOrder.class, reverseConvertGenerate = false)
public class FinArReceiptOrderBo extends BaseEntity {

    /**
     * 收款ID
     */
    private Long receiptId;

    /**
     * 收款编号
     */
    private String receiptCode;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 账号ID
     */
    @NotNull(message = "账号ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private FinAccountType accountType;

    /**
     * 收款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款方式
     */
    private FinPaymentMethod paymentMethod;

    /**
     * 收款日期
     */
    private LocalDate paymentDate;

    /**
     * 银行交易流水
     */
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unappliedAmount;

    /**
     * 收款状态
     */
    @NotBlank(message = "收款状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private FinArReceiptStatus receiptStatus;

    /**
     * 摘要
     */
    @NotBlank(message = "摘要不能为空", groups = {AddGroup.class, EditGroup.class})
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
