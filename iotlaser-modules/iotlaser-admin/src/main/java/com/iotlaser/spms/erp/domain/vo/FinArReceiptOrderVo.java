package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinArReceiptOrder;
import com.iotlaser.spms.erp.enums.FinAccountType;
import com.iotlaser.spms.erp.enums.FinArReceiptStatus;
import com.iotlaser.spms.erp.enums.FinPaymentMethod;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 收款单视图对象 erp_fin_ar_receipt_order
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinArReceiptOrder.class)
public class FinArReceiptOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收款ID
     */
    @ExcelProperty(value = "收款ID")
    private Long receiptId;

    /**
     * 收款编号
     */
    @ExcelProperty(value = "收款编号")
    private String receiptCode;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 账号ID
     */
    @ExcelProperty(value = "账号ID")
    private Long accountId;

    /**
     * 账户编码
     */
    @ExcelProperty(value = "账户编码")
    private String accountCode;

    /**
     * 账户名称
     */
    @ExcelProperty(value = "账户名称")
    private String accountName;

    /**
     * 账户类型
     */
    @ExcelProperty(value = "账户类型")
    private FinAccountType accountType;

    /**
     * 收款金额
     */
    @ExcelProperty(value = "收款金额")
    private BigDecimal paymentAmount;

    /**
     * 收款方式
     */
    @ExcelProperty(value = "收款方式")
    private FinPaymentMethod paymentMethod;

    /**
     * 收款日期
     */
    @ExcelProperty(value = "收款日期")
    private LocalDate paymentDate;

    /**
     * 银行交易流水
     */
    @ExcelProperty(value = "银行交易流水")
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    @ExcelProperty(value = "已核销金额")
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    @ExcelProperty(value = "未核销金额")
    private BigDecimal unappliedAmount;

    /**
     * 收款状态
     */
    @ExcelProperty(value = "收款状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_ar_receipt_status")
    private FinArReceiptStatus receiptStatus;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
