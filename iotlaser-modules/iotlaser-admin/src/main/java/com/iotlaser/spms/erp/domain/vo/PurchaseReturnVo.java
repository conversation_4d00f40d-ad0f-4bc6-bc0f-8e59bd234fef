package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 采购退货视图对象 erp_purchase_return
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseReturn.class)
public class PurchaseReturnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @ExcelProperty(value = "退货单ID")
    private Long returnId;

    /**
     * 退货单编号
     */
    @ExcelProperty(value = "退货单编号")
    private String returnCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;
    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 金额(含税)
     */
    @ExcelProperty(value = "金额(含税)")
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @ExcelProperty(value = "金额(不含税)")
    private BigDecimal amountExclusiveTax;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 退货日期
     */
    @ExcelProperty(value = "退货日期")
    private LocalDate returnDate;

    /**
     * 退货状态
     */
    @ExcelProperty(value = "退货状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_return_status")
    private PurchaseReturnStatus returnStatus;

    /**
     * 退货申请人ID
     */
    @ExcelProperty(value = "退货申请人ID")
    private Long applicantId;

    /**
     * 退货申请人
     */
    @ExcelProperty(value = "退货申请人")
    private String applicantName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
