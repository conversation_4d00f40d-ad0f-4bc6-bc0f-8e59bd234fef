package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 * 用于区分财务单据是与供应商采购相关还是日常行政管理相关
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Getter
@AllArgsConstructor
public enum BusinessType implements IDictEnum<String> {

    SUPPLIER("supplier", "供应商款项", "与供应商采购、入库、发票相关的财务业务"),
    ADMINISTRATIVE("administrative", "管理费用", "日常的公司运营管理费用，如租金、水电、差旅等");

    public static final String DICT_CODE = "erp_business_type";
    public static final String DICT_NAME = "财务业务类型";
    public static final String DICT_DESC = "区分供应商相关款项和日常管理费用";

    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
