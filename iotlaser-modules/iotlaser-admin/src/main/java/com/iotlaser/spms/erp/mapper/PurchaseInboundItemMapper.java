package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 采购入库明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
public interface PurchaseInboundItemMapper extends BaseMapperPlus<PurchaseInboundItem, PurchaseInboundItemVo> {

    default List<PurchaseInboundItem> queryByInboundId(Long inboundId) {
        return selectList(new LambdaQueryWrapper<PurchaseInboundItem>().eq(PurchaseInboundItem::getInboundId, inboundId));
    }

    default List<PurchaseInboundItemVo> queryVoByInboundId(Long inboundId) {
        return selectVoList(new LambdaQueryWrapper<PurchaseInboundItem>().eq(PurchaseInboundItem::getInboundId, inboundId));
    }

    /**
     * 删除采购入库明细表
     */
    default int deleteByInboundId(Long inboundId) {
        return delete(new LambdaQueryWrapper<PurchaseInboundItem>().eq(PurchaseInboundItem::getInboundId, inboundId));
    }

    /**
     * 计算采购入库明细总金额
     * ✅ 参照SaleOrderItemMapper.calculateTotalAmount实现
     *
     * @param inboundId 入库单ID
     * @return 价税分离计算结果
     */
    default TaxCalculationResultBo calculateTotalAmount(Long inboundId) {
        List<PurchaseInboundItemVo> items = Optional.ofNullable(queryVoByInboundId(inboundId))
            .orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (PurchaseInboundItemVo item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO);
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO);
            taxAmount = taxAmount.add(item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO);
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }

}
