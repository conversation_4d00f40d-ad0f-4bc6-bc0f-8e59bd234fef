package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 采购订单明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
public interface PurchaseOrderItemMapper extends BaseMapperPlus<PurchaseOrderItem, PurchaseOrderItemVo> {

    default List<PurchaseOrderItemVo> selectListByOrderId(Long orderId) {
        return selectVoList(new LambdaQueryWrapper<PurchaseOrderItem>().eq(PurchaseOrderItem::getOrderId, orderId));
    }

    default int deleteByOrderId(Long orderId) {
        return delete(new LambdaQueryWrapper<PurchaseOrderItem>().eq(PurchaseOrderItem::getOrderId, orderId));
    }

    /**
     * 计算采购订单明细总金额
     * ✅ 参照SaleOrderItemMapper.calculateTotalAmount实现
     *
     * @param orderId 订单ID
     * @return 价税分离计算结果
     */
    default TaxCalculationResultBo calculateTotalAmount(Long orderId) {
        List<PurchaseOrderItemVo> items = Optional.ofNullable(selectListByOrderId(orderId))
            .orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (PurchaseOrderItemVo item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO);
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO);
            taxAmount = taxAmount.add(item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO);
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }

}
