package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 采购退货明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface PurchaseReturnItemMapper extends BaseMapperPlus<PurchaseReturnItem, PurchaseReturnItemVo> {

    default List<PurchaseReturnItemVo> selectListByReturnId(Long returnId) {
        return selectVoList(new LambdaQueryWrapper<PurchaseReturnItem>().eq(PurchaseReturnItem::getReturnId, returnId));
    }

    default int deleteByOrderId(Long returnId) {
        return delete(new LambdaQueryWrapper<PurchaseReturnItem>().eq(PurchaseReturnItem::getReturnId, returnId));
    }

    /**
     * 计算采购退货明细总金额
     * ✅ 参照SaleOrderItemMapper.calculateTotalAmount实现
     *
     * @param returnId 退货单ID
     * @return 价税分离计算结果
     */
    default TaxCalculationResultBo calculateTotalAmount(Long returnId) {
        List<PurchaseReturnItemVo> items = Optional.ofNullable(selectListByReturnId(returnId))
            .orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (PurchaseReturnItemVo item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO);
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO);
            taxAmount = taxAmount.add(item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO);
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }
}
