package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.SaleReturnItem;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 销售退货明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
public interface SaleReturnItemMapper extends BaseMapperPlus<SaleReturnItem, SaleReturnItemVo> {

    default List<SaleReturnItemVo> queryByReturnId(Long returnId) {
        return selectVoList(new LambdaQueryWrapper<SaleReturnItem>().eq(SaleReturnItem::getReturnId, returnId));
    }

    default int deleteByReturnId(Long returnId) {
        return delete(new LambdaQueryWrapper<SaleReturnItem>().eq(SaleReturnItem::getReturnId, returnId));
    }

    /**
     * 计算销售退货明细总金额
     * ✅ 参照SaleOrderItemMapper.calculateTotalAmount实现
     *
     * @param returnId 退货单ID
     * @return 价税分离计算结果
     */
    default TaxCalculationResultBo calculateTotalAmount(Long returnId) {
        List<SaleReturnItemVo> items = Optional.ofNullable(queryByReturnId(returnId))
            .orElse(Collections.emptyList());
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal amountExclusiveTax = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;

        for (SaleReturnItemVo item : items) {
            if (item == null) continue;
            amount = amount.add(item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO);
            amountExclusiveTax = amountExclusiveTax.add(item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO);
            taxAmount = taxAmount.add(item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO);
        }

        return TaxCalculationResultBo.builder()
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .build();
    }
}
