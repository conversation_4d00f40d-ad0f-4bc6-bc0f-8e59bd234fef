package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinApInvoiceBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 供应商发票Service接口
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
public interface IFinApInvoiceService {

    /**
     * 查询供应商发票
     *
     * @param invoiceId 主键
     * @return 供应商发票
     */
    FinApInvoiceVo queryById(Long invoiceId);

    /**
     * 分页查询供应商发票列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 供应商发票分页列表
     */
    TableDataInfo<FinApInvoiceVo> queryPageList(FinApInvoiceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的供应商发票列表
     *
     * @param bo 查询条件
     * @return 供应商发票列表
     */
    List<FinApInvoiceVo> queryList(FinApInvoiceBo bo);

    /**
     * 新增供应商发票
     *
     * @param bo 供应商发票
     * @return 是否新增成功
     */
    Boolean insertByBo(FinApInvoiceBo bo);

    /**
     * 修改供应商发票
     *
     * @param bo 供应商发票
     * @return 是否修改成功
     */
    Boolean updateByBo(FinApInvoiceBo bo);

    /**
     * 校验并批量删除供应商发票信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 三单匹配 - 自动匹配
     *
     * @param invoiceId 发票ID
     * @return 匹配结果
     */
    Boolean autoMatchThreeWay(Long invoiceId);

    /**
     * 三单匹配 - 手工匹配
     *
     * @param invoiceId       发票ID
     * @param purchaseOrderId 采购订单ID
     * @param inboundId       入库单ID
     * @param matchedAmount   匹配金额
     * @param operatorId      操作人ID
     * @param operatorName    操作人姓名
     * @return 匹配结果
     */
    Boolean manualMatchThreeWay(Long invoiceId, Long purchaseOrderId, Long inboundId,
                                BigDecimal matchedAmount, Long operatorId, String operatorName);

    /**
     * 更新发票状态
     *
     * @param invoiceId 发票ID
     * @param newStatus 新状态
     * @return 是否更新成功
     */
    Boolean updateInvoiceStatus(Long invoiceId, String newStatus);

    /**
     * 从采购入库单生成应付发票
     *
     * @param inboundId     入库单ID
     * @param invoiceType   发票类型
     * @param invoiceDate   发票日期
     * @param invoiceNumber 发票号码
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 发票ID
     */
    Long generateFromPurchaseInbound(Long inboundId, String invoiceType, LocalDate invoiceDate,
                                     String invoiceNumber, Long operatorId, String operatorName);

    /**
     * 批量从采购入库单生成应付发票
     *
     * @param inboundIds   入库单ID列表
     * @param invoiceType  发票类型
     * @param invoiceDate  发票日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 批量生成结果
     */
    Map<String, Object> batchGenerateFromPurchaseInbounds(List<Long> inboundIds, String invoiceType,
                                                          LocalDate invoiceDate, Long operatorId, String operatorName);

    /**
     * 检查入库单是否已生成发票
     *
     * @param inboundId 入库单ID
     * @return 是否已生成发票
     */
    Boolean existsByInboundId(Long inboundId);

    /**
     * 根据入库单ID查询发票
     *
     * @param inboundId 入库单ID
     * @return 发票信息
     */
    FinApInvoiceVo queryByInboundId(Long inboundId);

    List<FinApInvoiceVo> getUnpaidInvoicesBefore(Long supplierId, LocalDate date);

    /**
     * 从采购订单明细生成应付单明细
     *
     * @param invoiceId            应付单ID
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @param operatorId           操作人ID
     * @param operatorName         操作人姓名
     * @return 是否生成成功
     */
    Boolean generateInvoiceItemsFromPurchaseOrderItems(Long invoiceId, List<Long> purchaseOrderItemIds,
                                                       Long operatorId, String operatorName);

    /**
     * 从采购入库单生成应付单
     *
     * @param inboundId    入库单ID
     * @param orderId      采购订单ID
     * @param supplierId   供应商ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 应付单ID
     */
    Long generateFromPurchaseInbound(Long inboundId, Long orderId, Long supplierId,
                                     Long operatorId, String operatorName);

    /**
     * 从入库单明细生成应付单明细
     *
     * @param invoiceId      应付单ID
     * @param inboundItemIds 入库单明细ID列表
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否生成成功
     */
    Boolean generateInvoiceItemsFromInboundItems(Long invoiceId, List<Long> inboundItemIds,
                                                 Long operatorId, String operatorName);

    /**
     * 从应付单生成付款单
     *
     * @param invoiceId     应付单ID
     * @param paymentAmount 付款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 付款单ID
     */
    Long generatePaymentOrderFromInvoice(Long invoiceId, BigDecimal paymentAmount,
                                         Long accountId, Long operatorId, String operatorName);
}
