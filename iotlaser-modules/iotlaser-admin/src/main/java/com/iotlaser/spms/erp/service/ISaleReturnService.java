package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleReturnBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售退货Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/08
 */
public interface ISaleReturnService {

    /**
     * 查询销售退货
     *
     * @param returnId 主键
     * @return 销售退货
     */
    SaleReturnVo queryById(Long returnId);

    /**
     * 分页查询销售退货列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售退货分页列表
     */
    TableDataInfo<SaleReturnVo> queryPageList(SaleReturnBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售退货列表
     *
     * @param bo 查询条件
     * @return 销售退货列表
     */
    List<SaleReturnVo> queryList(SaleReturnBo bo);

    /**
     * 新增销售退货
     *
     * @param bo 销售退货
     * @return 是否新增成功
     */
    SaleReturnVo insertByBo(SaleReturnBo bo);

    /**
     * 修改销售退货
     *
     * @param bo 销售退货
     * @return 是否修改成功
     */
    SaleReturnVo updateByBo(SaleReturnBo bo);

    /**
     * 校验并批量删除销售退货信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认销售退货单
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    Boolean confirmReturn(Long returnId);

    /**
     * 批量确认销售退货单
     *
     * @param returnIds 退货单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmReturns(Collection<Long> returnIds);

    /**
     * 收货确认（客户已退回货物）
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    Boolean receiveReturn(Long returnId);

    /**
     * 完成销售退货入库
     *
     * @param returnId 退货单ID
     * @return 是否完成成功
     */
    Boolean completeReturn(Long returnId);

    /**
     * 取消销售退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    Boolean cancelReturn(Long returnId, String reason);

    /**
     * 根据销售出库单创建退货单
     *
     * @param outboundVo 销售出库单
     * @return 创建的退货单
     */
    Boolean createFromSaleOutbound(SaleOutboundVo outboundVo);

    /**
     * 判断是否关联了指定上游单
     *
     * @param directSourceId 上游单ID
     * @return 是否存在
     */
    Boolean existsByDirectSourceId(Long directSourceId);

    /**
     * 根据WMS状态更新退货单状态
     *
     * @param returnId 退货单ID
     * @param wmsStatus WMS状态
     * @param itemQuantities 明细数量映射
     * @return 是否更新成功
     */
    Boolean updateStatusByWms(Long returnId, String wmsStatus, Map<Long, BigDecimal> itemQuantities);

    /**
     * 处理WMS异常
     *
     * @param returnId 退货单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    Boolean handleWmsException(Long returnId, String exceptionReason);
}
