package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinApInvoice;
import com.iotlaser.spms.erp.domain.FinApPaymentInvoiceLink;
import com.iotlaser.spms.erp.domain.FinApPaymentOrder;
import com.iotlaser.spms.erp.domain.bo.ApplyRecommendationBo;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentInvoiceLinkBo;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinApPaymentStatus;
import com.iotlaser.spms.erp.enums.FinPaymentMethod;
import com.iotlaser.spms.erp.mapper.FinApPaymentOrderMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IFinApPaymentInvoiceLinkService;
import com.iotlaser.spms.erp.service.IFinApPaymentOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 付款单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApPaymentOrderServiceImpl implements IFinApPaymentOrderService {

    private final FinApPaymentOrderMapper baseMapper;
    private final IFinApInvoiceService finApInvoiceService;
    private final IFinApPaymentInvoiceLinkService finApPaymentInvoiceLinkService;

    /**
     * 查询付款单
     *
     * @param paymentId 主键
     * @return 付款单
     */
    @Override
    public FinApPaymentOrderVo queryById(Long paymentId) {
        return baseMapper.selectVoById(paymentId);
    }

    /**
     * 分页查询付款单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单分页列表
     */
    @Override
    public TableDataInfo<FinApPaymentOrderVo> queryPageList(FinApPaymentOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApPaymentOrder> lqw = buildQueryWrapper(bo);
        Page<FinApPaymentOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的付款单列表
     *
     * @param bo 查询条件
     * @return 付款单列表
     */
    @Override
    public List<FinApPaymentOrderVo> queryList(FinApPaymentOrderBo bo) {
        LambdaQueryWrapper<FinApPaymentOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApPaymentOrder> buildQueryWrapper(FinApPaymentOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApPaymentOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApPaymentOrder::getPaymentId);
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentCode()), FinApPaymentOrder::getPaymentCode, bo.getPaymentCode());
        lqw.eq(bo.getSupplierId() != null, FinApPaymentOrder::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), FinApPaymentOrder::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getPaymentAmount() != null, FinApPaymentOrder::getPaymentAmount, bo.getPaymentAmount());
        if (bo.getPaymentMethod() != null) {
            lqw.eq(FinApPaymentOrder::getPaymentMethod, bo.getPaymentMethod());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getBankSerialNumber()), FinApPaymentOrder::getBankSerialNumber, bo.getBankSerialNumber());
        lqw.eq(bo.getAppliedAmount() != null, FinApPaymentOrder::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getUnappliedAmount() != null, FinApPaymentOrder::getUnappliedAmount, bo.getUnappliedAmount());
        lqw.eq(bo.getPaymentDate() != null, FinApPaymentOrder::getPaymentDate, bo.getPaymentDate());
        if (bo.getPaymentStatus() != null) {
            lqw.eq(FinApPaymentOrder::getPaymentStatus, bo.getPaymentStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApPaymentOrder::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增付款单
     *
     * @param bo 付款单
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinApPaymentOrderBo bo) {
        FinApPaymentOrder add = MapstructUtils.convert(bo, FinApPaymentOrder.class);
        validEntityBeforeSave(add);

        // TODO: [业务逻辑] - 根据付款类型分别处理
        // 1. 如果是采购付款，需要关联采购订单和供应商信息
        // 2. 如果是日常付款，直接创建付款单，无需关联
        if (add.getApplyType() == null) {
            throw new ServiceException("付款类型不能为空");
        }

        switch (add.getApplyType()) {
            case PURCHASE:
                // 采购付款
                if (add.getSupplierId() == null) {
                    throw new ServiceException("采购付款必须指定供应商");
                }
                // TODO: 校验供应商是否存在
                break;
            case DAILY:
                // 日常付款
                add.setSupplierId(null); // 日常付款无需关联供应商
                add.setSupplierName(null);
                break;
            default:
                throw new ServiceException("不支持的付款类型");
        }

        // 初始化付款单状态
        add.setPaymentStatus(FinApPaymentStatus.DRAFT);
        add.setAppliedAmount(BigDecimal.ZERO);
        add.setUnappliedAmount(add.getPaymentAmount());

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPaymentId(add.getPaymentId());
        }
        return flag;
    }

    /**
     * 修改付款单
     *
     * @param bo 付款单
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(FinApPaymentOrderBo bo) {
        // 1. 查询旧数据
        FinApPaymentOrder oldEntity = baseMapper.selectById(bo.getPaymentId());
        if (oldEntity == null) {
            throw new ServiceException("付款单不存在");
        }

        // 2. 校验是否允许修改
        if (oldEntity.getPaymentStatus() != FinApPaymentStatus.DRAFT) {
            throw new ServiceException("只有草稿状态的付款单才能修改");
        }

        FinApPaymentOrder update = MapstructUtils.convert(bo, FinApPaymentOrder.class);
        validEntityBeforeSave(update);

        // 3. 校验付款类型是否被修改
        if (update.getApplyType() != null && update.getApplyType() != oldEntity.getApplyType()) {
            throw new ServiceException("付款类型不能修改");
        }

        // 4. 根据付款类型进行校验
        if (oldEntity.getApplyType() == FinApplyType.PURCHASE) {
            if (update.getSupplierId() == null) {
                throw new ServiceException("采购付款必须指定供应商");
            }
            // TODO: 校验供应商是否存在
        } else if (oldEntity.getApplyType() == FinApplyType.DAILY) {
            update.setSupplierId(null); // 日常付款无需关联供应商
            update.setSupplierName(null);
        }

        // 5. 更新未核销金额
        // 如果付款金额被修改，需要同步更新未核销金额
        if (update.getPaymentAmount() != null &&
            update.getPaymentAmount().compareTo(oldEntity.getPaymentAmount()) != 0) {
            // 未核销金额 = 新付款金额 - 已核销金额
            BigDecimal newUnappliedAmount = update.getPaymentAmount().subtract(oldEntity.getAppliedAmount());
            if (newUnappliedAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("付款金额不能小于已核销金额");
            }
            update.setUnappliedAmount(newUnappliedAmount);
        }

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApPaymentOrder entity) {
        // 校验付款单编号唯一性
        if (StringUtils.isNotBlank(entity.getPaymentCode())) {
            LambdaQueryWrapper<FinApPaymentOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinApPaymentOrder::getPaymentCode, entity.getPaymentCode());
            if (entity.getPaymentId() != null) {
                wrapper.ne(FinApPaymentOrder::getPaymentId, entity.getPaymentId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("付款单编号已存在：" + entity.getPaymentCode());
            }
        }

        //校验付款金额
        if (entity.getPaymentAmount() != null && entity.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("付款金额必须大于0");
        }
    }

    /**
     * 校验并批量删除付款单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 业务校验：检查是否可以删除
            validateBeforeDelete(ids);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 创建付款申请
     *
     * @param supplierId    供应商ID
     * @param supplierCode  供应商编码
     * @param supplierName  供应商名称
     * @param paymentAmount 付款金额
     * @param paymentMethod 付款方式
     * @param applicantId   申请人ID
     * @param applicantName 申请人姓名
     * @param remark        备注
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createPaymentApplication(Long supplierId, String supplierCode, String supplierName,
                                            BigDecimal paymentAmount, FinPaymentMethod paymentMethod,
                                            Long applicantId, String applicantName, String remark) {
        try {
            FinApPaymentOrder paymentOrder = new FinApPaymentOrder();

            // 生成付款单编号
            paymentOrder.setPaymentCode(generatePaymentCode());
            paymentOrder.setSummary("付款申请-" + supplierName);

            // 供应商信息
            paymentOrder.setSupplierId(supplierId);
            paymentOrder.setSupplierName(supplierName);

            // 付款信息
            paymentOrder.setPaymentAmount(paymentAmount);
            paymentOrder.setPaymentMethod(paymentMethod);
            paymentOrder.setAppliedAmount(BigDecimal.ZERO);
            paymentOrder.setUnappliedAmount(paymentAmount);

            // 状态信息
            paymentOrder.setPaymentStatus(FinApPaymentStatus.PENDING_APPROVAL);
            paymentOrder.setRemark(remark);

            boolean result = baseMapper.insert(paymentOrder) > 0;

            if (result) {
                log.info("付款申请创建成功 - 供应商: {}, 金额: {}, 申请人: {}",
                    supplierName, paymentAmount, applicantName);
            }

            return result;
        } catch (Exception e) {
            log.error("付款申请创建失败 - 供应商: {}, 错误: {}", supplierName, e.getMessage(), e);
            throw new ServiceException("付款申请创建失败：" + e.getMessage());
        }
    }

    /**
     * 审批付款申请
     *
     * @param paymentId     付款单ID
     * @param approverId    审批人ID
     * @param approverName  审批人姓名
     * @param approveResult 审批结果 (APPROVED/REJECTED)
     * @param approveRemark 审批备注
     * @return 是否审批成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean approvePayment(Long paymentId, Long approverId, String approverName,
                                  String approveResult, String approveRemark) {
        try {
            FinApPaymentOrder paymentOrder = baseMapper.selectById(paymentId);
            if (paymentOrder == null) {
                throw new ServiceException("付款单不存在");
            }

            if (FinApPaymentStatus.PENDING_APPROVAL != paymentOrder.getPaymentStatus()) {
                throw new ServiceException("付款单状态不允许审批");
            }

            if ("APPROVED".equals(approveResult)) {
                paymentOrder.setPaymentStatus(FinApPaymentStatus.APPROVED);
            } else if ("REJECTED".equals(approveResult)) {
                paymentOrder.setPaymentStatus(FinApPaymentStatus.REJECTED);
            } else {
                throw new ServiceException("无效的审批结果");
            }

            if (StringUtils.isNotBlank(approveRemark)) {
                paymentOrder.setRemark(paymentOrder.getRemark() + "\n审批意见：" + approveRemark);
            }

            boolean result = baseMapper.updateById(paymentOrder) > 0;

            if (result) {
                log.info("付款审批完成 - 付款单: {}, 结果: {}, 审批人: {}",
                    paymentOrder.getPaymentCode(), approveResult, approverName);
            }

            return result;
        } catch (Exception e) {
            log.error("付款审批失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("付款审批失败：" + e.getMessage());
        }
    }

    /**
     * 付款单核销到发票
     *
     * @param paymentId     付款单ID
     * @param invoiceId     发票ID
     * @param appliedAmount 核销金额
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 是否核销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyToInvoice(Long paymentId, Long invoiceId, BigDecimal appliedAmount,
                                  Long operatorId, String operatorName) {
        try {
            // 获取付款单信息
            FinApPaymentOrder paymentOrder = baseMapper.selectById(paymentId);
            if (paymentOrder == null) {
                throw new ServiceException("付款单不存在");
            }

            if (!FinApPaymentStatus.APPROVED.getValue().equals(paymentOrder.getPaymentStatus())) {
                throw new ServiceException("付款单状态不允许核销，当前状态：" + paymentOrder.getPaymentStatus());
            }

            // 获取发票信息
            FinApInvoiceVo invoice = finApInvoiceService.queryById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (!"APPROVED".equals(invoice.getInvoiceStatus())) {
                throw new ServiceException("发票状态不允许核销");
            }

            // 校验核销金额
            if (appliedAmount.compareTo(paymentOrder.getUnappliedAmount()) > 0) {
                throw new ServiceException("核销金额不能超过付款单未核销金额");
            }

            BigDecimal invoiceUnpaidAmount = invoice.getAmount().subtract(
                getInvoiceAppliedAmount(invoiceId));
            if (appliedAmount.compareTo(invoiceUnpaidAmount) > 0) {
                throw new ServiceException("核销金额不能超过发票未付金额");
            }

            // 创建核销记录
            boolean linkResult = createPaymentInvoiceLink(paymentOrder, invoice,
                appliedAmount, operatorId, operatorName);

            // 更新付款单核销金额
            paymentOrder.setAppliedAmount(paymentOrder.getAppliedAmount().add(appliedAmount));
            paymentOrder.setUnappliedAmount(paymentOrder.getUnappliedAmount().subtract(appliedAmount));

            // 更新付款单状态
            if (paymentOrder.getUnappliedAmount().compareTo(BigDecimal.ZERO) == 0) {
                paymentOrder.setPaymentStatus(FinApPaymentStatus.FULLY_APPLIED);
            } else {
                paymentOrder.setPaymentStatus(FinApPaymentStatus.PARTIALLY_APPLIED);
            }

            boolean paymentResult = baseMapper.updateById(paymentOrder) > 0;

            // 更新发票状态
            BigDecimal newInvoiceAppliedAmount = getInvoiceAppliedAmount(invoiceId);
            String newInvoiceStatus;
            if (newInvoiceAppliedAmount.compareTo(invoice.getAmount()) == 0) {
                newInvoiceStatus = "FULLY_PAID";
            } else {
                newInvoiceStatus = "PARTIALLY_PAID";
            }

            boolean invoiceResult = finApInvoiceService.updateInvoiceStatus(invoiceId, newInvoiceStatus);

            if (linkResult && paymentResult && invoiceResult) {
                log.info("付款核销成功 - 付款单: {}, 发票: {}, 核销金额: {}",
                    paymentOrder.getPaymentCode(), invoice.getInvoiceCode(), appliedAmount);
                return true;
            } else {
                throw new ServiceException("核销操作失败");
            }

        } catch (Exception e) {
            log.error("付款核销失败 - 付款单ID: {}, 发票ID: {}, 错误: {}",
                paymentId, invoiceId, e.getMessage(), e);
            throw new ServiceException("付款核销失败：" + e.getMessage());
        }
    }

    /**
     * 创建付款发票核销记录
     */
    private boolean createPaymentInvoiceLink(FinApPaymentOrder payment, FinApInvoiceVo invoice,
                                             BigDecimal appliedAmount, Long appliedById, String appliedByName) {
        FinApPaymentInvoiceLink link = new FinApPaymentInvoiceLink();

        // 关联ID（核心字段）
        link.setPaymentId(payment.getPaymentId());
        link.setInvoiceId(invoice.getInvoiceId());

        // 核销信息
        link.setAppliedAmount(appliedAmount);
        link.setCancellationDate(LocalDate.now());  // 修复：使用LocalDate而不是Date
        link.setStatus("1"); // 有效状态

        return finApPaymentInvoiceLinkService.insertByBo(
            MapstructUtils.convert(link, FinApPaymentInvoiceLinkBo.class));
    }

    /**
     * 获取发票已核销金额
     */
    private BigDecimal getInvoiceAppliedAmount(Long invoiceId) {
        return finApPaymentInvoiceLinkService.getAppliedAmountByInvoiceId(invoiceId);
    }

    /**
     * 提交付款单审批
     *
     * @param paymentId    付款单ID
     * @param submitById   提交人ID
     * @param submitByName 提交人姓名
     * @return 是否提交成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitForApproval(Long paymentId, Long submitById, String submitByName) {
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            if ( FinApPaymentStatus.DRAFT!=payment.getPaymentStatus()) {
                throw new ServiceException("付款单状态不允许提交审批");
            }

            payment.setPaymentStatus( FinApPaymentStatus.PENDING_APPROVAL);
            boolean result = baseMapper.updateById(payment) > 0;

            if (result) {
                log.info("付款单提交审批成功 - 付款单: {}, 提交人: {}", payment.getPaymentCode(), submitByName);

                // 发送审批通知
                try {
                    sendApprovalNotification(payment, submitById, submitByName);
                } catch (Exception e) {
                    log.warn("发送审批通知失败 - 付款单: {}, 错误: {}", payment.getPaymentCode(), e.getMessage());
                    // 不影响主流程，继续执行
                }
            }

            return result;
        } catch (Exception e) {
            log.error("付款单提交审批失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("付款单提交审批失败：" + e.getMessage());
        }
    }

    /**
     * 审批付款单
     *
     * @param paymentId      付款单ID
     * @param approvalAction 审批动作 (APPROVE/REJECT)
     * @param approvalRemark 审批备注
     * @param approverId     审批人ID
     * @param approverName   审批人姓名
     * @return 是否审批成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean approvePayment(Long paymentId, String approvalAction, String approvalRemark,
                                  Long approverId, String approverName) {
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            if ( FinApPaymentStatus.PENDING_APPROVAL != payment.getPaymentStatus()) {
                throw new ServiceException("付款单状态不允许审批");
            }

            if ("APPROVE".equals(approvalAction)) {
                payment.setPaymentStatus( FinApPaymentStatus.APPROVED);
            } else if ("REJECT".equals(approvalAction)) {
                payment.setPaymentStatus( FinApPaymentStatus.REJECTED);
            } else {
                throw new ServiceException("无效的审批动作");
            }

            payment.setRemark(approvalRemark);
            boolean result = baseMapper.updateById(payment) > 0;

            if (result) {
                log.info("付款单审批成功 - 付款单: {}, 动作: {}, 审批人: {}",
                    payment.getPaymentCode(), approvalAction, approverName);
                // TODO: 发送审批结果通知
            }

            return result;
        } catch (Exception e) {
            log.error("付款单审批失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("付款单审批失败：" + e.getMessage());
        }
    }

    /**
     * 取消付款单
     *
     * @param paymentId    付款单ID
     * @param cancelById   取消人ID
     * @param cancelByName 取消人姓名
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelPayment(Long paymentId, Long cancelById, String cancelByName, String cancelReason) {
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            if ( FinApPaymentStatus.FULLY_APPLIED == payment.getPaymentStatus() ||  FinApPaymentStatus.CANCELLED ==payment.getPaymentStatus()) {
                throw new ServiceException("付款单状态不允许取消");
            }

            payment.setPaymentStatus( FinApPaymentStatus.CANCELLED);
            payment.setRemark(cancelReason);
            boolean result = baseMapper.updateById(payment) > 0;

            if (result) {
                log.info("付款单取消成功 - 付款单: {}, 取消人: {}, 原因: {}",
                    payment.getPaymentCode(), cancelByName, cancelReason);
            }

            return result;
        } catch (Exception e) {
            log.error("付款单取消失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("付款单取消失败：" + e.getMessage());
        }
    }

    /**
     * 删除前业务校验
     *
     * @param ids 待删除的ID集合
     */
    private void validateBeforeDelete(Collection<Long> ids) {
        for (Long id : ids) {
            FinApPaymentOrder payment = baseMapper.selectById(id);
            if (payment != null) {
                // 检查付款单状态，已审批或已核销的不能删除
                if ("APPROVED".equals(payment.getPaymentStatus()) ||
                    "PARTIALLY_APPLIED".equals(payment.getPaymentStatus()) ||
                    "FULLY_APPLIED".equals(payment.getPaymentStatus())) {
                    throw new ServiceException("付款单[" + payment.getPaymentCode() + "]已审批或已核销，不能删除");
                }

                // 检查是否有核销记录
                BigDecimal appliedAmount = getPaymentAppliedAmount(id);
                if (appliedAmount.compareTo(BigDecimal.ZERO) > 0) {
                    throw new ServiceException("付款单[" + payment.getPaymentCode() + "]存在核销记录，不能删除");
                }
            }
        }
    }

    /**
     * 获取付款单已核销金额
     *
     * @param paymentId 付款单ID
     * @return 已核销金额
     */
    private BigDecimal getPaymentAppliedAmount(Long paymentId) {
        return finApPaymentInvoiceLinkService.getAppliedAmountByPaymentId(paymentId);
    }

    /**
     * 生成付款单编号
     */
    private String generatePaymentCode() {
        return "PAY" + System.currentTimeMillis();
    }

    /**
     * 发送审批通知
     *
     * @param payment      付款单
     * @param submitById   提交人ID
     * @param submitByName 提交人姓名
     */
    private void sendApprovalNotification(FinApPaymentOrder payment, Long submitById, String submitByName) {
        try {
            // 构建通知内容
            String title = "付款单审批通知";
            String content = String.format("付款单【%s】已提交审批，请及时处理。\n" +
                    "供应商：%s\n" +
                    "付款金额：%s\n" +
                    "提交人：%s\n" +
                    "提交时间：%s",
                payment.getPaymentCode(),
                payment.getSupplierName(),
                payment.getPaymentAmount(),
                submitByName,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            // 记录通知日志（实际项目中可以集成消息通知服务）
            log.info("审批通知已发送 - 标题: {}, 内容: {}", title, content);

            // TODO: 集成实际的消息通知服务
            // notificationService.sendNotification(approverIds, title, content);

        } catch (Exception e) {
            log.error("发送审批通知失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取智能核销建议
     *
     * @param paymentId 付款单ID
     * @return 核销建议列表
     */
    public List<ApplyRecommendationBo> getApplyRecommendations(Long paymentId) {
        try {
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            // 校验付款单状态
            if ( FinApPaymentStatus.CONFIRMED!=payment.getPaymentStatus()) {
                throw new ServiceException("只有已确认的付款单才能获取核销建议");
            }

            List<ApplyRecommendationBo> recommendations = new ArrayList<>();

            // 基于供应商查找待核销发票
            List<FinApInvoiceVo> candidateInvoices = findCandidateInvoices(payment);

            // 计算每个发票的匹配度和建议核销金额
            for (FinApInvoiceVo invoice : candidateInvoices) {
                ApplyRecommendationBo recommendation = calculateRecommendation(payment, invoice);
                if (recommendation.getMatchScore() > 0.5) { // 匹配度大于50%才推荐
                    recommendations.add(recommendation);
                }
            }

            // 按匹配度排序
            recommendations.sort((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()));

            // 限制推荐数量
            int maxRecommendations = 10;
            if (recommendations.size() > maxRecommendations) {
                recommendations = recommendations.subList(0, maxRecommendations);
            }

            log.info("获取付款单核销建议成功 - 付款单: {}, 建议数量: {}",
                payment.getPaymentCode(), recommendations.size());

            return recommendations;
        } catch (Exception e) {
            log.error("获取付款单核销建议失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("获取核销建议失败：" + e.getMessage());
        }
    }

    /**
     * 部分核销
     *
     * @param paymentId     付款单ID
     * @param invoiceId     发票ID
     * @param appliedAmount 核销金额
     * @param applyReason   核销原因
     * @return 是否核销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean partialApply(Long paymentId, Long invoiceId, BigDecimal appliedAmount, String applyReason) {
        try {
            // 校验付款单
            FinApPaymentOrder payment = baseMapper.selectById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            if (!"CONFIRMED".equals(payment.getPaymentStatus())) {
                throw new ServiceException("只有已确认的付款单才能核销");
            }

            // 校验发票
            // TODO: 获取发票信息进行校验
            // FinApInvoice invoice = finApInvoiceService.queryById(invoiceId);

            // 校验核销金额
            if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销金额必须大于0");
            }

            // 获取付款单剩余可核销金额
            BigDecimal remainingAmount = getRemainingApplyAmount(paymentId);
            if (appliedAmount.compareTo(remainingAmount) > 0) {
                throw new ServiceException("核销金额不能超过付款单剩余金额：" + remainingAmount);
            }

            // 创建核销记录
            Long linkId = createApplyRecord(paymentId, invoiceId, appliedAmount, applyReason);

            // 更新付款单和发票的核销状态
            updateApplyStatus(paymentId, invoiceId, appliedAmount);

            log.info("部分核销成功 - 付款单: {}, 发票ID: {}, 核销金额: {}, 核销记录ID: {}",
                payment.getPaymentCode(), invoiceId, appliedAmount, linkId);

            return true;
        } catch (Exception e) {
            log.error("部分核销失败 - 付款单ID: {}, 发票ID: {}, 错误: {}", paymentId, invoiceId, e.getMessage(), e);
            throw new ServiceException("部分核销失败：" + e.getMessage());
        }
    }

    /**
     * 核销撤销
     *
     * @param linkId        核销记录ID
     * @param reverseReason 撤销原因
     * @return 是否撤销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean reverseApply(Long linkId, String reverseReason) {
        try {
            // 获取核销记录
            // TODO: 获取核销记录详情
            // FinApPaymentInvoiceLink link = finApPaymentInvoiceLinkService.queryById(linkId);

            if (StringUtils.isBlank(reverseReason)) {
                throw new ServiceException("撤销原因不能为空");
            }

            // 校验核销记录状态
            // 只有已核销状态的记录才能撤销
            // if (!"APPLIED".equals(link.getLinkStatus())) {
            //     throw new ServiceException("只有已核销状态的记录才能撤销");
            // }

            // 更新核销记录状态为已撤销
            // link.setLinkStatus("REVERSED");
            // link.setReverseReason(reverseReason);
            // link.setReverseTime(LocalDateTime.now());
            // link.setReverseById(LoginHelper.getUserId());
            // link.setReverseByName(LoginHelper.getLoginUser().getNickname());

            // 更新付款单和发票的核销状态
            // reverseApplyStatus(link.getPaymentId(), link.getInvoiceId(), link.getAppliedAmount());

            log.info("核销撤销成功 - 核销记录ID: {}, 撤销原因: {}", linkId, reverseReason);

            return true;
        } catch (Exception e) {
            log.error("核销撤销失败 - 核销记录ID: {}, 错误: {}", linkId, e.getMessage(), e);
            throw new ServiceException("核销撤销失败：" + e.getMessage());
        }
    }

    /**
     * 核销差异处理
     *
     * @param linkId           核销记录ID
     * @param differenceAmount 差异金额
     * @param differenceReason 差异原因
     * @param handleType       处理方式 (ADJUST/WRITE_OFF)
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleApplyDifference(Long linkId, BigDecimal differenceAmount,
                                         String differenceReason, String handleType) {
        try {
            if (differenceAmount == null || differenceAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ServiceException("差异金额不能为0");
            }

            if (StringUtils.isBlank(differenceReason)) {
                throw new ServiceException("差异原因不能为空");
            }

            // 获取核销记录
            // TODO: 获取核销记录详情
            // FinApPaymentInvoiceLink link = finApPaymentInvoiceLinkService.queryById(linkId);

            // 根据处理方式执行不同逻辑
            if ("ADJUST".equals(handleType)) {
                // 调整核销金额
                handleDifferenceByAdjust(linkId, differenceAmount, differenceReason);
            } else if ("WRITE_OFF".equals(handleType)) {
                // 差异核销
                handleDifferenceByWriteOff(linkId, differenceAmount, differenceReason);
            } else {
                throw new ServiceException("无效的差异处理方式：" + handleType);
            }

            log.info("核销差异处理成功 - 核销记录ID: {}, 差异金额: {}, 处理方式: {}",
                linkId, differenceAmount, handleType);

            return true;
        } catch (Exception e) {
            log.error("核销差异处理失败 - 核销记录ID: {}, 错误: {}", linkId, e.getMessage(), e);
            throw new ServiceException("核销差异处理失败：" + e.getMessage());
        }
    }

    /**
     * 查找候选发票
     */
    private List<FinApInvoiceVo> findCandidateInvoices(FinApPaymentOrder payment) {
        // TODO: 实现候选发票查找逻辑
        // 基于供应商ID查找未完全核销的发票
        // 基于时间范围过滤
        // 基于金额范围过滤
        // 排除已核销完成的发票
        return new ArrayList<>();
    }

    /**
     * 计算核销建议
     */
    private ApplyRecommendationBo calculateRecommendation(FinApPaymentOrder payment, FinApInvoiceVo invoice) {
        ApplyRecommendationBo recommendation = new ApplyRecommendationBo();

        // 计算匹配度
        double matchScore = 0.0;

        // 供应商匹配 (权重40%)
        if (Objects.equals(payment.getSupplierId(), invoice.getPayeeId())) {
            matchScore += 0.4;
        }

        // 金额匹配 (权重30%)
        if (payment.getUnappliedAmount() != null && invoice.getAmount() != null) {
            BigDecimal amountDiff = payment.getUnappliedAmount().subtract(invoice.getAmount()).abs();
            BigDecimal tolerance = payment.getUnappliedAmount().multiply(new BigDecimal("0.05")); // 5%容差
            if (amountDiff.compareTo(tolerance) <= 0) {
                matchScore += 0.3;
            }
        }

        // 时间匹配 (权重20%)
        if (payment.getPaymentDate() != null && invoice.getInvoiceDate() != null) {
            long daysDiff = Math.abs(payment.getPaymentDate().toEpochDay() - invoice.getInvoiceDate().toEpochDay());
            if (daysDiff <= 30) { // 30天内
                matchScore += 0.2 * (1 - daysDiff / 30.0);
            }
        }

        // 币种匹配 (权重10%)  暂无币种

        recommendation.setInvoiceId(invoice.getInvoiceId());
        recommendation.setInvoiceCode(invoice.getInvoiceCode());
        recommendation.setInvoiceAmount(invoice.getAmount());
        recommendation.setMatchScore(matchScore);

        // 建议核销金额 = min(付款单剩余金额, 发票剩余金额)
        BigDecimal paymentRemaining = getRemainingApplyAmount(payment.getPaymentId());
        BigDecimal invoiceRemaining = getInvoiceRemainingAmount(invoice.getInvoiceId());
        recommendation.setRecommendedAmount(paymentRemaining.min(invoiceRemaining));

        return recommendation;
    }

    /**
     * 获取付款单剩余可核销金额
     */
    private BigDecimal getRemainingApplyAmount(Long paymentId) {
        // TODO: 计算付款单剩余可核销金额
        // 付款单总金额 - 已核销金额
        return BigDecimal.ZERO;
    }

    /**
     * 获取发票剩余金额
     */
    private BigDecimal getInvoiceRemainingAmount(Long invoiceId) {
        // TODO: 计算发票剩余金额
        // 发票总金额 - 已核销金额
        return BigDecimal.ZERO;
    }

    /**
     * 创建核销记录
     */
    private Long createApplyRecord(Long paymentId, Long invoiceId, BigDecimal appliedAmount, String applyReason) {
        // TODO: 创建核销记录到数据库
        // 返回核销记录ID
        return System.currentTimeMillis();
    }

    /**
     * 更新核销状态
     */
    private void updateApplyStatus(Long paymentId, Long invoiceId, BigDecimal appliedAmount) {
        // TODO: 更新付款单和发票的核销状态
        // 更新付款单已核销金额和状态
        // 更新发票已核销金额和状态
    }

    /**
     * 撤销核销状态
     */
    private void reverseApplyStatus(Long paymentId, Long invoiceId, BigDecimal appliedAmount) {
        // TODO: 撤销付款单和发票的核销状态
        // 减少付款单已核销金额
        // 减少发票已核销金额
        // 重新计算核销状态
    }

    /**
     * 通过调整处理差异
     */
    private void handleDifferenceByAdjust(Long linkId, BigDecimal differenceAmount, String differenceReason) {
        // TODO: 实现调整处理逻辑
        // 调整核销记录的核销金额
        // 更新相关单据的核销状态
        // 记录调整日志
    }

    /**
     * 通过核销处理差异
     */
    private void handleDifferenceByWriteOff(Long linkId, BigDecimal differenceAmount, String differenceReason) {
        // TODO: 实现差异核销逻辑
        // 创建差异核销记录
        // 更新核销状态
        // 记录差异处理日志
    }

    /**
     * 批量核销付款单到发票
     *
     * @param links        核销明细列表
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 批量核销结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> batchApplyToInvoices(List<FinApPaymentInvoiceLink> links,
                                                    Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (FinApPaymentInvoiceLink item : links) {
                try {
                    Boolean success = applyToInvoice(item.getPaymentId(), item.getInvoiceId(),
                        item.getAppliedAmount(), operatorId, operatorName);

                    if (success) {
                        successList.add(Map.of(
                            "paymentId", item.getPaymentId(),
                            "invoiceId", item.getInvoiceId(),
                            "appliedAmount", item.getAppliedAmount(),
                            "status", "SUCCESS"
                        ));
                    } else {
                        failureList.add(Map.of(
                            "paymentId", item.getPaymentId(),
                            "invoiceId", item.getInvoiceId(),
                            "status", "ERROR",
                            "reason", "核销操作失败"
                        ));
                    }
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "paymentId", item.getPaymentId(),
                        "invoiceId", item.getInvoiceId(),
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", links.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量核销完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                links.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量核销失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量核销失败：" + e.getMessage());
        }
    }

    /**
     * 撤销付款核销
     *
     * @param writeoffId   核销记录ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelWriteoff(Long writeoffId, Long operatorId, String operatorName) {
        try {
            // TODO: 获取核销记录
            // FinApPaymentInvoiceLink link = finApPaymentInvoiceLinkService.queryById(writeoffId);

            // 校验核销记录状态
            // 删除核销记录
            boolean deleteResult = finApPaymentInvoiceLinkService.deleteWithValidByIds(List.of(writeoffId), false);

            if (deleteResult) {
                // TODO: 重新计算付款单和发票的核销状态
                log.info("撤销付款核销成功 - 核销记录ID: {}, 操作人: {}", writeoffId, operatorName);
                return true;
            } else {
                throw new ServiceException("撤销核销记录失败");
            }
        } catch (Exception e) {
            log.error("撤销付款核销失败 - 核销记录ID: {}, 错误: {}", writeoffId, e.getMessage(), e);
            throw new ServiceException("撤销付款核销失败：" + e.getMessage());
        }
    }

    /**
     * 查询付款单的核销记录
     *
     * @param paymentId 付款单ID
     * @return 核销记录列表
     */
    @Override
    public List<Map<String, Object>> getWriteoffRecords(Long paymentId) {
        try {
            // TODO: 查询核销记录
            List<Map<String, Object>> records = new ArrayList<>();

            // 示例数据结构
            // records = finApPaymentInvoiceLinkService.getRecordsByPaymentId(paymentId);

            return records;
        } catch (Exception e) {
            log.error("查询付款单核销记录失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            throw new ServiceException("查询核销记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取可核销的发票列表
     *
     * @param supplierId 供应商ID
     * @param amount     付款金额
     * @return 可核销的发票列表
     */
    @Override
    public List<FinApInvoiceVo> getWriteoffableInvoices(Long supplierId, BigDecimal amount) {
        try {
            if (supplierId == null) {
                throw new ServiceException("供应商ID不能为空");
            }

            log.info("查询可核销发票列表 - 供应商ID: {}, 付款金额: {}", supplierId, amount);

            // 构建查询条件
            LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();

            // 供应商匹配
            wrapper.eq(FinApInvoice::getPayeeId, supplierId);

            // 发票状态为已审批或部分付款（可以继续核销）
            wrapper.in(FinApInvoice::getInvoiceStatus,
                FinApInvoiceStatus.APPROVED.getValue(),
                FinApInvoiceStatus.PARTIALLY_PAID.getValue());

            // 按发票日期排序，优先核销较早的发票
            wrapper.orderByAsc(FinApInvoice::getInvoiceDate);

            // 限制查询数量，避免返回过多数据
            wrapper.last("LIMIT 100");

            // TODO: 修复finApInvoiceService.list()方法调用
            // List<FinApInvoice> invoices = finApInvoiceService.list(wrapper);
            List<FinApInvoiceVo> result = new ArrayList<>();

            // TODO: 临时返回空列表，待实现完整的发票查询逻辑
            // for (FinApInvoice invoice : invoices) {
            //     // 计算发票的未核销金额
            //     BigDecimal appliedAmount = finApPaymentInvoiceLinkService.getAppliedAmountByInvoiceId(invoice.getInvoiceId());
            //     BigDecimal unappliedAmount = AmountCalculationUtils.safeSubtract(invoice.getAmount(), appliedAmount);
            //
            //     // 只返回有未核销金额的发票
            //     if (unappliedAmount != null && unappliedAmount.compareTo(BigDecimal.ZERO) > 0) {
            //         FinApInvoiceVo invoiceVo = MapstructUtils.convert(invoice, FinApInvoiceVo.class);
            //         // 设置未核销金额（临时变量，用于显示）
            //         // TODO: 在FinApInvoiceVo中添加setUnappliedAmount()方法
            //         // invoiceVo.setUnappliedAmount(unappliedAmount);
            //         result.add(invoiceVo);
            //     }
            //
            //     // 如果指定了付款金额，当累计未核销金额达到付款金额时可以停止
            //     if (amount != null && result.size() >= 10) {
            //         break; // 限制返回数量，提高性能
            //     }
            // }

            log.info("查询可核销发票完成 - 供应商ID: {}, 找到可核销发票: {} 张", supplierId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取可核销发票列表失败 - 供应商ID: {}, 错误: {}", supplierId, e.getMessage(), e);
            throw new ServiceException("获取可核销发票列表失败：" + e.getMessage());
        }
    }

    // ==================== 付款核销核心逻辑 ====================

    /**
     * TODO: [付款核销逻辑] - 参考文档 docs/design/README_FINANCE.md
     * 将一笔付款单核销到一张或多张应付发票。
     *
     * @param paymentId 要进行核销的付款单ID
     * @param links     核销明细，包含要核销的发票ID和本次核销的金额
     * @return 是否核销成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean applyPaymentToInvoices(Long paymentId, List<FinApPaymentInvoiceLink> links) {
        // 1. 查询付款单信息，并校验其状态是否为“已审核”或“部分核销”。
        FinApPaymentOrderVo paymentOrder = queryById(paymentId);
        if (paymentOrder == null ||
            !(FinApPaymentStatus.APPROVED.getValue().equals(paymentOrder.getPaymentStatus()) ||
                FinApPaymentStatus.PARTIALLY_APPLIED.getValue().equals(paymentOrder.getPaymentStatus()))) {
            throw new ServiceException("付款单状态不正确，无法核销");
        }

        // 2. 校验本次要核销的总金额是否超过付款单的未核销金额。
        BigDecimal totalWriteOffAmount = links.stream()
            .map(FinApPaymentInvoiceLink::getAppliedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalWriteOffAmount.compareTo(paymentOrder.getUnappliedAmount()) > 0) {
            throw new ServiceException("核销总金额超过付款单未核销金额");
        }

        // 3. 遍历核销明细列表。
        // for (FinApPaymentInvoiceLink item : writeOffItems) {
        //     // 4. 查询应付发票信息，并校验其状态是否允许被核销。
        //     FinApInvoiceVo invoice = finApInvoiceService.queryById(item.getInvoiceId());
        //     // ...
        //
        //     // 5. 创建核销关联记录 (FinApPaymentInvoiceLink)。
        //     finApPaymentInvoiceLinkService.createLink(paymentId, item.getInvoiceId(), item.getAppliedAmount());
        //
        //     // 6. 更新发票的已付金额和状态 (PARTIALLY_PAID, FULLY_PAID)。
        //     finApInvoiceService.updatePaymentStatus(item.getInvoiceId(), item.getAppliedAmount());
        // }

        // 7. 更新付款单的已核销金额和状态 (PARTIALLY_APPLIED, FULLY_APPLIED)。
        // updatePaymentStatus(paymentId, totalWriteOffAmount);

        return true;
    }
}
