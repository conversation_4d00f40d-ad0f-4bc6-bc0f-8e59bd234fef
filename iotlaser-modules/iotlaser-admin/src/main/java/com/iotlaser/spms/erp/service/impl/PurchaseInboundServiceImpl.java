package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.*;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.mapper.PurchaseInboundItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseInboundMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.IInboundService;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_INBOUND_CODE;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 采购入库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInboundServiceImpl implements IPurchaseInboundService {

    private final PurchaseInboundMapper baseMapper;
    private final PurchaseInboundItemMapper itemMapper;
    private final IFinApInvoiceService finApInvoiceService;
    private final IFinApPaymentInvoiceLinkService finApPaymentInvoiceLinkService;
    private final IFinAccountLedgerService finAccountLedgerService;
    private final Gen gen;
    private final IInventoryService inventoryService;
    private final ICompanyService companyService;
    private final IProductService productService;
    private final IInboundService inboundService;
    private final IPurchaseReturnService purchaseReturnService;
    private final IPurchaseInboundItemService itemService;
    private final IPurchaseOrderService purchaseOrderService;

    /**
     * 查询采购入库
     *
     * @param inboundId 主键
     * @return 采购入库
     */
    @Override
    public PurchaseInboundVo queryById(Long inboundId) {
        return baseMapper.selectVoById(inboundId);
    }

    /**
     * 分页查询采购入库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库分页列表
     */
    @Override
    public TableDataInfo<PurchaseInboundVo> queryPageList(PurchaseInboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseInbound> lqw = buildQueryWrapper(bo);
        Page<PurchaseInboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购入库列表
     *
     * @param bo 查询条件
     * @return 采购入库列表
     */
    @Override
    public List<PurchaseInboundVo> queryList(PurchaseInboundBo bo) {
        LambdaQueryWrapper<PurchaseInbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据采购订单ID查询采购入库单列表
     *
     * @param orderId 采购订单ID
     * @return 采购入库单列表
     */
    @Override
    public List<PurchaseInboundVo> selectListByPurchaseOrderId(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("采购订单ID不能为空");
        }
        return baseMapper.selectListByDirectSourceId(orderId);
    }

    private LambdaQueryWrapper<PurchaseInbound> buildQueryWrapper(PurchaseInboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseInbound::getInboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getInboundCode()), PurchaseInbound::getInboundCode, bo.getInboundCode());
        lqw.eq(bo.getSourceId() != null, PurchaseInbound::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), PurchaseInbound::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(PurchaseInbound::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, PurchaseInbound::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), PurchaseInbound::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(PurchaseInbound::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getSupplierId() != null, PurchaseInbound::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseInbound::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getInboundTime() != null, PurchaseInbound::getInboundTime, bo.getInboundTime());
        if (bo.getInboundStatus() != null) {
            lqw.eq(PurchaseInbound::getInboundStatus, bo.getInboundStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseInbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginInboundTime") != null && params.get("endInboundTime") != null,
            PurchaseInbound::getInboundTime, params.get("beginInboundTime"), params.get("endInboundTime"));
        return lqw;
    }

    /**
     * 新增采购入库
     *
     * @param bo 采购入库
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseInboundVo insertByBo(PurchaseInboundBo bo) {
        try {
            // 生成入库单号
            if (StringUtils.isEmpty(bo.getInboundCode())) {
                bo.setInboundCode(gen.code(ERP_PURCHASE_INBOUND_CODE));
            }
            // 设置初始状态
            if (bo.getInboundStatus() == null) {
                bo.setInboundStatus(PurchaseInboundStatus.DRAFT);
            }
            // 设置入库时间
            if (bo.getInboundTime() == null) {
                bo.setInboundTime(LocalDateTime.now());
            }
            // 填充冗余字段
            fillRedundantFields(bo);
            // 转换为实体并校验
            PurchaseInbound add = MapstructUtils.convert(bo, PurchaseInbound.class);
            validEntityBeforeSave(add);
            // 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增采购入库失败");
            }

            bo.setInboundId(add.getInboundId());

            log.info("新增采购入库成功：{}", add.getInboundCode());
            return MapstructUtils.convert(add, PurchaseInboundVo.class);
        } catch (Exception e) {
            log.error("新增采购入库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增采购入库失败：" + e.getMessage());
        }
    }


    /**
     * 修改采购入库
     *
     * @param bo 采购入库
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseInboundVo updateByBo(PurchaseInboundBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);
            // 转换为实体并校验
            PurchaseInbound update = MapstructUtils.convert(bo, PurchaseInbound.class);
            validEntityBeforeSave(update);
            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改采购入库失败");
            }
            log.info("修改采购入库成功：{}", update.getInboundCode());
            return MapstructUtils.convert(update, PurchaseInboundVo.class);
        } catch (Exception e) {
            log.error("修改采购入库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改采购入库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 采购入库单实体
     */
    private void validEntityBeforeSave(PurchaseInbound entity) {
        //TODO 校验入库单号是否重复

    }

    /**
     * 校验并批量删除采购入库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验采购入库单是否可以删除
            List<PurchaseInbound> inbounds = baseMapper.selectByIds(ids);
            for (PurchaseInbound inbound : inbounds) {
                // 检查入库单状态，只有草稿状态的入库单才能删除
                if (PurchaseInboundStatus.DRAFT != inbound.getInboundStatus()) {
                    throw new ServiceException("采购入库单【" + inbound.getInboundCode() + "】状态为【" +
                        inbound.getInboundStatus() + "】，不允许删除");
                }

                // 检查是否有关联的库存变动记录
                // 注意：草稿状态的入库单通常还未产生实际库存变动，可以安全删除
                log.debug("检查采购入库单库存变动记录 - 入库单ID: {}", inbound.getInboundId());

                // 级联删除采购入库明细
                // TODO: 添加existsByInboundId和getItemIdsByInboundId方法
                // if (itemService.existsByInboundId(inbound.getInboundId())) {
                //     List<Long> itemIds = itemService.getItemIdsByInboundId(inbound.getInboundId());
                //     if (!itemIds.isEmpty()) {
                //         itemService.deleteWithValidByIds(itemIds, false);
                //         log.info("级联删除采购入库明细，入库单：{}，明细数量：{}", inbound.getInboundName(), itemIds.size());
                //     }
                // }

                log.info("删除采购入库单校验通过：{}", inbound.getInboundCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购入库单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购入库单失败：" + e.getMessage());
        }
    }

    /**
     * 确认采购入库单
     *
     * @param inboundId 入库单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmInbound(Long inboundId) {

        PurchaseInboundVo inboundVo = inboundValid(inboundId);

        // 校验状态
        if (PurchaseInboundStatus.DRAFT != inboundVo.getInboundStatus()) {
            throw new ServiceException("只有草稿状态的采购入库单才能确认");
        }

        PurchaseInbound update = new PurchaseInbound();
        update.setInboundId(inboundId);
        // 更新状态为待入库
        update.setInboundStatus(PurchaseInboundStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(update) > 0;

        // TODO: [ERP→WMS数据推送实现] - 优先级: HIGH - 参考文档 docs/design/README_FLOW.md
        // 采购入库单提交后，必须实现完整的数据链路传递：
        //
        // 1. 数据完整性校验：
        //    - 校验采购入库单状态：必须为 CONFIRMED 状态
        //    - 校验明细数据完整性：物料、数量、批次、库位等
        //    - 校验业务规则：供应商、合同、价格等信息
        //
        // 2. WMS入库单创建：
        //    - 调用 inboundService.createFromPurchaseInbound(inboundVo)
        //    - 传递完整的物料清单、批次信息、质检要求
        //    - 设置正确的源单据关联关系
        //
        // 3. 状态同步机制：
        //    - 建立ERP入库单与WMS入库单的双向关联
        //    - 实现状态变更的实时同步
        //    - 提供数据一致性检查和修复机制
        //
        // 4. 异常处理策略：
        //    - WMS创建失败时的回滚机制
        //    - 数据不一致时的告警和修复
        //    - 提供手工补偿和重试机制

        if (result) {
            createWmsInboundFromPurchaseInbound(inboundVo);
        }

        return result;
    }

    /**
     * 创建仓库入库单
     *
     * @param inboundId 采购入库ID
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createInbound(Long inboundId) {
        try {
            // 校验采购入库单
            PurchaseInboundVo inboundVo = inboundValid(inboundId);
            // 调用入库单服务创建入库单
            Boolean createResult = inboundService.createFromPurchaseInbound(inboundVo);
            if (!createResult) {
                throw new ServiceException("创建仓库入库单失败");
            }
            log.info("创建仓库入库单成功 - 采购入库单: {}", inboundVo.getInboundCode());
            return true;
        } catch (Exception e) {
            log.error("创建仓库入库单失败 - 采购入库ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("创建仓库入库单失败：" + e.getMessage());
        }
    }

    /**
     * 创建采购退货单
     *
     * @param inboundId 采购入库ID
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createPurchaseReturn(Long inboundId) {
        try {
            // 校验采购入库单
            PurchaseInboundVo inboundVo = inboundValid(inboundId);
            // 调用入库单服务创建入库单
            Boolean createResult = purchaseReturnService.createFromPurchaseInbound(inboundVo);
            if (!createResult) {
                throw new ServiceException("创建仓库入库单失败");
            }
            log.info("创建仓库入库单成功 - 采购入库单: {}", inboundVo.getInboundCode());
            return true;
        } catch (Exception e) {
            log.error("创建仓库入库单失败 - 采购入库ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("创建仓库入库单失败：" + e.getMessage());
        }
    }

    /**
     * 确认入库和创建入库前置验证
     *
     * @param inboundId 订单 ID
     * @return 订单信息
     */
    private PurchaseInboundVo inboundValid(Long inboundId) {
        if (inboundId == null) {
            throw new ServiceException("采购入库ID不能为空");
        }
        PurchaseInboundVo inboundVo = baseMapper.selectVoById(inboundId);
        if (inboundVo == null) {
            throw new ServiceException("采购入库单不存在");
        }
        List<PurchaseInboundItem> items = itemMapper.queryByInboundId(inboundId);
        if (items.isEmpty()) {
            throw new ServiceException("入库明细不能为空");
        }
        for (PurchaseInboundItem item : items) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("入库明细【" + item.getProductName() + "】应入库数量不能小于等于0");
            }
            if (item.getFinishQuantity() == null || item.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("入库明细【" + item.getProductName() + "】实收数量不能小于等于0");
            }
            if (item.getQuantity().compareTo(item.getFinishQuantity()) != 0) {
                throw new ServiceException("入库明细【" + item.getProductName() + "】实收数量不能大于或小于应入库数量");
            }
        }
        inboundVo.setItems(MapstructUtils.convert(items, PurchaseInboundItemVo.class));
        return inboundVo;
    }

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 入库单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmInbounds(Collection<Long> inboundIds) {
        for (Long inboundId : inboundIds) {
            confirmInbound(inboundId);
        }
        return true;
    }

    /**
     * 完成采购入库
     *
     * @param inboundId 入库单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeInbound(Long inboundId) {

        PurchaseInboundVo inboundVo = inboundValid(inboundId);
        // 校验状态
        if (!PurchaseInboundStatus.PENDING_WAREHOUSE.equals(inboundVo.getInboundStatus())) {
            throw new ServiceException("只有待入库状态的采购入库单才能完成");
        }
        try {
            PurchaseInbound uppdate = new PurchaseInbound();
            uppdate.setInboundId(inboundId);
            // 先处理库存记录，如果失败则不更新状态
            uppdate.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            // 库存记录处理成功后，更新状态
            boolean result = baseMapper.updateById(uppdate) > 0;
            if (result) {
                log.info("采购入库单【{}】完成入库，库存记录已生成", inboundVo.getInboundCode());
            }
            return result;
        } catch (Exception e) {
            log.error("采购入库单【{}】完成入库失败：{}", inboundVo.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("完成入库失败：" + e.getMessage());
        }
    }

    /**
     * 根据采购订单创建入库单
     *
     * @param orderVo 采购订单
     * @return 创建的入库单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseOrder(PurchaseOrderVo orderVo) {
        // TODO: [从采购订单创建] - 这是 "采购到入库" 流程的关键步骤。
        // 此方法应由 PurchaseOrderServiceImpl 在订单确认后调用。
        LoginUser loginUser = getLoginUser();
        // 创建入库单
        PurchaseInbound add = new PurchaseInbound();
        add.setInboundCode(gen.code(ERP_PURCHASE_INBOUND_CODE));

        add.setSourceId(orderVo.getSourceId());
        add.setSourceCode(orderVo.getSourceCode());
        add.setSourceType(orderVo.getSourceType());

        add.setDirectSourceId(orderVo.getOrderId());
        add.setDirectSourceCode(orderVo.getOrderCode());
        add.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);

        add.setSupplierId(orderVo.getSupplierId());
        add.setSupplierName(orderVo.getSupplierName());
        add.setInboundStatus(PurchaseInboundStatus.DRAFT);
        add.setInboundTime(LocalDateTime.now());
        if (loginUser != null) {
            add.setHandlerId(loginUser.getUserId());
            add.setHandlerName(loginUser.getNickname());
        }
        add.setSummary("基于采购订单【" + orderVo.getOrderCode() + "】创建");

        // 创建入库单
        int result = baseMapper.insert(add);
        if (result <= 0) {
            throw new ServiceException("创建入库单失败");
        }

        List<PurchaseInboundItem> inboundItems = new ArrayList<>();
        for (PurchaseOrderItemVo orderItem : orderVo.getItems()) {
            PurchaseInboundItem inboundItem = new PurchaseInboundItem();
            inboundItem.setInboundId(add.getInboundId());
            inboundItem.setProductId(orderItem.getProductId());
            inboundItem.setProductCode(orderItem.getProductCode());
            inboundItem.setProductName(orderItem.getProductName());
            inboundItem.setUnitId(orderItem.getUnitId());
            inboundItem.setUnitCode(orderItem.getUnitCode());
            inboundItem.setUnitName(orderItem.getUnitName());
            inboundItem.setQuantity(orderItem.getQuantity());
            inboundItem.setFinishQuantity(BigDecimal.ZERO);
            inboundItem.setPrice(orderItem.getPrice());
            inboundItem.setPriceExclusiveTax(orderItem.getPriceExclusiveTax());
            inboundItem.setAmount(orderItem.getAmount());
            inboundItem.setAmountExclusiveTax(orderItem.getAmountExclusiveTax());
            inboundItem.setTaxRate(orderItem.getTaxRate());
            inboundItem.setTaxAmount(orderItem.getTaxAmount());
            inboundItems.add(inboundItem);
        }
        boolean insertBatch = itemMapper.insertBatch(inboundItems);
        if (!insertBatch) {
            throw new ServiceException("基于采购订单创建入库单失败");
        }
        return true;
    }

    /**
     * 检查是否存在指定订单ID的入库单
     *
     * @param orderId 订单ID
     * @return 是否存在
     */
    @Override
    public Boolean existsByOrderId(Long orderId) {
        return baseMapper.existsByDirectSourceId(orderId);
    }

    /**
     * 从采购入库单生成应付发票
     *
     * @param inboundId     入库单ID
     * @param invoiceType   发票类型
     * @param invoiceDate   发票日期
     * @param invoiceNumber 发票号码
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 发票ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateApInvoiceFromInbound(Long inboundId, String invoiceType,
                                             LocalDate invoiceDate, String invoiceNumber,
                                             Long operatorId, String operatorName) {
        try {
            // 获取入库单信息
            PurchaseInbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("采购入库单不存在");
            }

            // 校验入库单状态
            if (PurchaseInboundStatus.COMPLETED != inbound.getInboundStatus()) {
                throw new ServiceException("只有已完成的入库单才能生成发票");
            }

            // 检查是否已经生成过发票
            if (finApInvoiceService.existsByInboundId(inboundId)) {
                throw new ServiceException("该入库单已生成发票，不能重复生成");
            }

            // 调用财务服务生成发票
            Long invoiceId = finApInvoiceService.generateFromPurchaseInbound(inboundId,
                invoiceType, invoiceDate, invoiceNumber, operatorId, operatorName);

            // 更新入库单状态，标记已生成发票
            inbound.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            baseMapper.updateById(inbound);

            log.info("从采购入库单生成应付发票成功 - 入库单: {}, 发票ID: {}, 操作人: {}",
                inbound.getInboundCode(), invoiceId, operatorName);

            return invoiceId;
        } catch (Exception e) {
            log.error("从采购入库单生成应付发票失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("生成应付发票失败：" + e.getMessage());
        }
    }

    /**
     * 批量从入库单生成应付发票
     *
     * @param inboundIds   入库单ID列表
     * @param invoiceType  发票类型
     * @param invoiceDate  发票日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 批量生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateApInvoices(List<Long> inboundIds, String invoiceType,
                                                       LocalDate invoiceDate, Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (Long inboundId : inboundIds) {
                try {
                    // 为每个入库单生成发票号码
                    String invoiceNumber = generateInvoiceNumber();

                    Long invoiceId = generateApInvoiceFromInbound(inboundId, invoiceType,
                        invoiceDate, invoiceNumber, operatorId, operatorName);

                    successList.add(Map.of(
                        "inboundId", inboundId,
                        "invoiceId", invoiceId,
                        "invoiceNumber", invoiceNumber,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "inboundId", inboundId,
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", inboundIds.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量生成应付发票完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                inboundIds.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量生成应付发票失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量生成应付发票失败：" + e.getMessage());
        }
    }

    /**
     * 入库完成后自动生成应付单
     *
     * @param inboundId    入库单ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateInvoiceAfterInboundComplete(Long inboundId, Long operatorId, String operatorName) {
        try {
            if (inboundId == null) {
                throw new ServiceException("入库单ID不能为空");
            }

            // 校验入库单状态为已完成
            PurchaseInbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("入库单不存在");
            }

            if (PurchaseInboundStatus.COMPLETED != inbound.getInboundStatus()) {
                throw new ServiceException("只有已完成的入库单才能生成应付单");
            }

            // 检查是否已生成应付单
            if (finApInvoiceService.existsByInboundId(inboundId)) {
                throw new ServiceException("该入库单已生成应付单，不能重复生成");
            }

            log.info("开始从入库单生成应付单 - 入库单ID: {}, 入库单编号: {}, 操作人: {}",
                inboundId, inbound.getInboundCode(), operatorName);

            // 从入库单信息生成应付单主表
            Long invoiceId = finApInvoiceService.generateFromPurchaseInbound(
                inboundId,
                inbound.getDirectSourceId(),
                inbound.getSupplierId(),
                operatorId,
                operatorName
            );

            if (invoiceId == null) {
                throw new ServiceException("应付单生成失败");
            }

            // 从入库单明细生成应付单明细
            List<PurchaseInboundItem> inboundItems = itemMapper.queryByInboundId(inboundId);

            if (!inboundItems.isEmpty()) {
                List<Long> inboundItemIds = inboundItems.stream()
                    .map(PurchaseInboundItem::getItemId)
                    .toList();

                Boolean itemsResult = finApInvoiceService.generateInvoiceItemsFromInboundItems(
                    invoiceId, inboundItemIds, operatorId, operatorName);

                if (!itemsResult) {
                    throw new ServiceException("应付单明细生成失败");
                }
            }

            log.info("从入库单生成应付单成功 - 入库单: {}, 应付单ID: {}, 操作人: {}",
                inbound.getInboundCode(), invoiceId, operatorName);

            return true;
        } catch (Exception e) {
            log.error("从入库单生成应付单失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("生成应付单失败：" + e.getMessage());
        }
    }

    /**
     * 采购业务完整流程：从入库完成到付款出账
     *
     * @param inboundId     入库单ID
     * @param paymentAmount 付款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 完整的业务结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completePurchaseBusinessFlow(Long inboundId, BigDecimal paymentAmount,
                                                            Long accountId, Long operatorId, String operatorName) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行采购业务完整流程 - 入库单ID: {}, 付款金额: {}, 操作人: {}",
                inboundId, paymentAmount, operatorName);

            // 入库完成后生成应付单
            Boolean invoiceResult = generateInvoiceAfterInboundComplete(inboundId, operatorId, operatorName);
            if (!invoiceResult) {
                throw new ServiceException("生成应付单失败");
            }

            // 获取生成的应付单ID
            FinApInvoiceVo invoice = finApInvoiceService.queryByInboundId(inboundId);
            if (invoice == null) {
                throw new ServiceException("未找到生成的应付单");
            }
            Long invoiceId = invoice.getInvoiceId();

            // 从应付单生成付款单
            Long paymentId = finApInvoiceService.generatePaymentOrderFromInvoice(
                invoiceId, paymentAmount, accountId, operatorId, operatorName);

            if (paymentId == null) {
                throw new ServiceException("生成付款单失败");
            }

            // 进行付款单与应付单核销
            Boolean applyResult = finApPaymentInvoiceLinkService.applyPaymentToInvoice(
                paymentId, invoiceId, paymentAmount, "采购业务完整流程自动核销");

            if (!applyResult) {
                throw new ServiceException("付款单与应付单核销失败");
            }

            // 生成账户支出流水
            Boolean ledgerResult = finAccountLedgerService.generateExpenseFromPaymentOrder(
                paymentId, accountId, "采购业务完整流程支出");

            if (!ledgerResult) {
                throw new ServiceException("生成账户支出流水失败");
            }

            // 返回完整的业务结果
            result.put("success", true);
            result.put("inboundId", inboundId);
            result.put("invoiceId", invoiceId);
            result.put("paymentId", paymentId);
            result.put("accountId", accountId);
            result.put("paymentAmount", paymentAmount);
            result.put("message", "采购业务完整流程执行成功");

            log.info("采购业务完整流程执行成功 - 入库单: {}, 应付单: {}, 付款单: {}, 付款金额: {}",
                inboundId, invoiceId, paymentId, paymentAmount);

            return result;
        } catch (Exception e) {
            log.error("采购业务完整流程执行失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);

            result.put("success", false);
            result.put("inboundId", inboundId);
            result.put("error", e.getMessage());
            result.put("message", "采购业务完整流程执行失败");

            throw new ServiceException("采购业务完整流程执行失败：" + e.getMessage());
        }
    }

    /**
     * 生成发票号码
     */
    private String generateInvoiceNumber() {
        return "INV" + System.currentTimeMillis();
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(PurchaseInboundBo bo) {
        // 填充采购订单信息
//        if (bo.getOrderId() != null) {
//            PurchaseOrderVo purchaseOrder = purchaseOrderService.queryById(bo.getOrderId());
//            if (purchaseOrder != null) {
//                bo.setOrderCode(purchaseOrder.getOrderCode());
//                bo.setSupplierId(purchaseOrder.getSupplierId());
//                bo.setSupplierName(purchaseOrder.getSupplierName());
//            }
//        }
        // 填充供应商信息
//        if (bo.getSupplierId() != null && StringUtils.isEmpty(bo.getSupplierName())) {
//            CompanyVo supplier = companyService.queryById(bo.getSupplierId());
//            if (supplier != null) {
//                bo.setSupplierName(supplier.getCompanyName());
//            }
//        }
        // 获取当前登录用户信息
        LoginUser loginUser = getLoginUser();
        if (loginUser != null) {
            // 如果收货负责人为空，设置为当前用户
            if (bo.getHandlerId() == null) {
                bo.setHandlerId(loginUser.getUserId());
                bo.setHandlerName(loginUser.getNickname());
            }
        }
    }

    // TODO: [ERP→WMS数据推送方法实现] - 优先级: HIGH - 参考文档 docs/design/README_FLOW.md
    // 需要实现以下数据推送方法：

    /**
     * 创建WMS入库单从采购入库单
     * @param inboundVo 采购入库单
     */
    private void createWmsInboundFromPurchaseInbound(PurchaseInboundVo inboundVo) {
        try {
            log.info("开始创建WMS入库单 - 采购入库单: {}", inboundVo.getInboundCode());

            // TODO: 实现完整的数据推送逻辑
            // 1. 数据完整性校验
            validatePurchaseInboundForWms(inboundVo);

            // 2. 调用WMS服务创建入库单
            Boolean createResult = inboundService.createFromPurchaseInbound(inboundVo);

            if (!createResult) {
                throw new ServiceException("WMS入库单创建失败");
            }

            log.info("WMS入库单创建成功 - 采购入库单: {}", inboundVo.getInboundCode());

        } catch (Exception e) {
            log.error("创建WMS入库单失败 - 采购入库单: {}, 错误: {}",
                inboundVo.getInboundCode(), e.getMessage(), e);
            // 不抛出异常，避免影响ERP入库单确认流程
            // 可以考虑添加重试机制或手工补偿机制
        }
    }

    /**
     * 校验采购入库单数据完整性
     * @param inboundVo 采购入库单
     */
    private void validatePurchaseInboundForWms(PurchaseInboundVo inboundVo) {
        if (inboundVo.getItems() == null || inboundVo.getItems().isEmpty()) {
            throw new ServiceException("采购入库单明细不能为空");
        }

        for (PurchaseInboundItemVo item : inboundVo.getItems()) {
            if (item.getProductId() == null) {
                throw new ServiceException("产品ID不能为空");
            }
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("入库数量必须大于0");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusByWms(Long inboundId, String wmsInboundCode, Map<Long, BigDecimal> actualQuantities) {
        try {
            log.info("WMS状态回传 - 采购入库单ID: {}, WMS入库单: {}", inboundId, wmsInboundCode);

            // 1. 查询采购入库单
            PurchaseInbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                log.error("WMS状态回传失败 - 采购入库单不存在: {}", inboundId);
                return false;
            }

            // 2. 校验状态是否允许更新
            if (inbound.getInboundStatus() == PurchaseInboundStatus.COMPLETED) {
                log.warn("WMS状态回传 - 采购入库单已完成，跳过更新: {}", inboundId);
                return true;
            }

            // 3. 更新采购入库单状态
            inbound.setInboundStatus(PurchaseInboundStatus.COMPLETED);
            inbound.setInboundTime(LocalDateTime.now());
            inbound.setRemark(StringUtils.isNotBlank(inbound.getRemark()) ?
                inbound.getRemark() + " [WMS入库完成: " + wmsInboundCode + "]" :
                "[WMS入库完成: " + wmsInboundCode + "]");

            // 4. 更新明细实际入库数量（如果提供）
            if (actualQuantities != null && !actualQuantities.isEmpty()) {
                updateActualQuantities(inboundId, actualQuantities);
            }

            int result = baseMapper.updateById(inbound);
            if (result > 0) {
                log.info("WMS状态回传成功 - 采购入库单: {}", inbound.getInboundCode());

                // 5. 触发后续业务流程（如生成应付单）
                try {
                    triggerDownstreamProcesses(inbound);
                } catch (Exception e) {
                    log.error("WMS状态回传成功，但触发后续业务流程失败 - 采购入库单: {}, 错误: {}",
                        inbound.getInboundCode(), e.getMessage(), e);
                    // 不抛出异常，避免影响主流程
                }

                // 6. 回传采购订单状态更新
                try {
                    updatePurchaseOrderStatus(inbound);
                } catch (Exception e) {
                    log.error("WMS状态回传成功，但更新采购订单状态失败 - 采购入库单: {}, 错误: {}",
                        inbound.getInboundCode(), e.getMessage(), e);
                    // 不抛出异常，避免影响主流程
                }

                return true;
            } else {
                log.error("WMS状态回传失败 - 数据库更新失败: {}", inboundId);
                return false;
            }

        } catch (Exception e) {
            log.error("WMS状态回传异常 - 采购入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("WMS状态回传失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleWmsException(Long inboundId, String exceptionReason) {
        try {
            log.warn("WMS异常回传 - 采购入库单ID: {}, 异常原因: {}", inboundId, exceptionReason);

            PurchaseInbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                log.error("WMS异常回传失败 - 采购入库单不存在: {}", inboundId);
                return false;
            }

            // 更新状态为异常
            inbound.setInboundStatus(PurchaseInboundStatus.EXCEPTION);
            inbound.setRemark(StringUtils.isNotBlank(inbound.getRemark()) ?
                inbound.getRemark() + " [WMS异常: " + exceptionReason + "]" :
                "[WMS异常: " + exceptionReason + "]");

            int result = baseMapper.updateById(inbound);
            if (result > 0) {
                log.info("WMS异常回传处理成功 - 采购入库单: {}", inbound.getInboundCode());
                return true;
            } else {
                log.error("WMS异常回传处理失败 - 数据库更新失败: {}", inboundId);
                return false;
            }

        } catch (Exception e) {
            log.error("WMS异常回传处理异常 - 采购入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("WMS异常回传处理失败：" + e.getMessage());
        }
    }

    /**
     * 更新明细实际入库数量
     */
    private void updateActualQuantities(Long inboundId, Map<Long, BigDecimal> actualQuantities) {
        // TODO: 实现明细数量更新逻辑
        log.info("更新采购入库明细实际数量 - 入库单ID: {}, 数量信息: {}", inboundId, actualQuantities);
    }

    /**
     * 触发下游业务流程
     */
    private void triggerDownstreamProcesses(PurchaseInbound inbound) {
        try {
            // 触发应付单生成
            log.info("触发下游业务流程 - 采购入库单: {}", inbound.getInboundCode());
            // 注意：操作人信息需要从上下文中获取或传递
            LoginUser loginUser = getLoginUser();
            Long operatorId = loginUser != null ? loginUser.getUserId() : null;
            String operatorName = loginUser != null ? loginUser.getNickname() : "System";
            generateInvoiceAfterInboundComplete(inbound.getInboundId(), operatorId, operatorName);
        } catch (Exception e) {
            log.error("触发下游业务流程失败 - 采购入库单: {}, 错误: {}",
                inbound.getInboundCode(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新采购订单状态
     * <p>
     * 根据入库完成情况更新采购订单的收货状态
     *
     * @param inbound 采购入库单
     */
    private void updatePurchaseOrderStatus(PurchaseInbound inbound) {
        try {
            // 检查是否关联采购订单（directSourceType为PURCHASE_ORDER且directSourceId不为空）
            if (inbound.getDirectSourceId() == null ||
                inbound.getDirectSourceType() != DirectSourceType.PURCHASE_ORDER) {
                log.debug("采购入库单【{}】没有关联采购订单，跳过订单状态更新", inbound.getInboundCode());
                return;
            }

            log.info("开始更新采购订单状态 - 订单ID: {}, 入库单: {}",
                inbound.getDirectSourceId(), inbound.getInboundCode());

            // 查询入库单明细，统计实际入库数量
            PurchaseInboundItemBo queryBo = new PurchaseInboundItemBo();
            queryBo.setInboundId(inbound.getInboundId());
            List<PurchaseInboundItemVo> inboundItems = itemService.queryList(queryBo);

            if (inboundItems.isEmpty()) {
                log.warn("采购入库单【{}】没有明细，跳过订单状态更新", inbound.getInboundCode());
                return;
            }

            // ✅ 采购订单明细与入库明细通过产品ID关联的正确设计
            // 采购订单明细 ←→ 采购入库明细：通过产品ID关联，支持分批入库
            // 一个采购订单明细可以对应多个入库明细（分批入库场景）
            // 一个入库明细只对应一个产品，但可能来自不同的采购订单明细

            // 构建实际收货数量映射 (productId -> finishQuantity)
            Map<Long, BigDecimal> finishQuantityMap = new HashMap<>();

            // 按产品ID汇总入库数量，这是正确的业务逻辑
            for (PurchaseInboundItemVo item : inboundItems) {
                if (item.getProductId() != null && item.getFinishQuantity() != null) {
                    // 按产品ID汇总实际入库数量
                    finishQuantityMap.merge(item.getProductId(), item.getFinishQuantity(), BigDecimal::add);
                }
            }

            if (!finishQuantityMap.isEmpty()) {
                // 调用采购订单服务更新收货状态
                Boolean updateResult = purchaseOrderService.updateReceivedStatus(inbound.getDirectSourceId(), finishQuantityMap);
                if (updateResult) {
                    log.info("成功更新采购订单收货状态 - 订单ID: {}, 入库单: {}",
                        inbound.getDirectSourceId(), inbound.getInboundCode());
                } else {
                    log.error("更新采购订单收货状态失败 - 订单ID: {}, 入库单: {}",
                        inbound.getDirectSourceId(), inbound.getInboundCode());
                }
            } else {
                log.warn("采购入库单【{}】没有有效的订单明细关联，跳过订单状态更新", inbound.getInboundCode());
            }

        } catch (Exception e) {
            log.error("更新采购订单状态异常 - 入库单: {}, 错误: {}",
                inbound.getInboundCode(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

}
