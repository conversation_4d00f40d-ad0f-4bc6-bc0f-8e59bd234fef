package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.mapper.PurchaseOrderItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseOrderMapper;
import com.iotlaser.spms.erp.service.IPurchaseOrderItemService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购订单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseOrderItemServiceImpl implements IPurchaseOrderItemService {

    private final PurchaseOrderItemMapper baseMapper;
    private final PurchaseOrderMapper purchaseOrderMapper;
    private final IProductService productService;

    /**
     * 查询采购订单明细
     *
     * @param itemId 主键
     * @return 采购订单明细
     */
    @Override
    public PurchaseOrderItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询采购订单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseOrderItemVo> queryPageList(PurchaseOrderItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseOrderItem> lqw = buildQueryWrapper(bo);
        Page<PurchaseOrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购订单明细列表
     *
     * @param bo 查询条件
     * @return 采购订单明细列表
     */
    @Override
    public List<PurchaseOrderItemVo> queryList(PurchaseOrderItemBo bo) {
        LambdaQueryWrapper<PurchaseOrderItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseOrderItem> buildQueryWrapper(PurchaseOrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseOrderItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseOrderItem::getItemId);
        lqw.eq(bo.getOrderId() != null, PurchaseOrderItem::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProductId() != null, PurchaseOrderItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseOrderItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseOrderItem::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseOrderItem::getStatus, bo.getStatus());
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), PurchaseOrderItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * 新增采购订单明细
     *
     * @param bo 采购订单明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PurchaseOrderItemBo bo) {
        // 填充冗余信息
        fillRedundantFields(bo);
        PurchaseOrderItem add = MapstructUtils.convert(bo, PurchaseOrderItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());

            // ✅ 明细保存后自动更新主订单金额合计
            if (add.getOrderId() != null) {
                updateOrderTotalAmounts(add.getOrderId());
            }
        }
        return flag;
    }


    /**
     * 修改采购订单明细
     *
     * @param bo 采购订单明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PurchaseOrderItemBo bo) {
        try {
            // 填充冗余信息
            fillRedundantFields(bo);
            PurchaseOrderItem item = MapstructUtils.convert(bo, PurchaseOrderItem.class);
            // 验证实体
            validEntityBeforeSave(item);

            int result = baseMapper.updateById(item);
            boolean flag = result > 0;

            // ✅ 明细更新后自动更新主订单金额合计
            if (flag && item.getOrderId() != null) {
                updateOrderTotalAmounts(item.getOrderId());
            }

            return flag;
        } catch (Exception e) {
            throw new ServiceException("更新采购订单明细失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseOrderItem entity) {
        // 校验同一订单中产品不能重复
        if (entity.getOrderId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseOrderItem::getOrderId, entity.getOrderId());
            wrapper.eq(PurchaseOrderItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(PurchaseOrderItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一采购订单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除采购订单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 获取要删除的明细，用于后续更新主订单金额
        List<PurchaseOrderItem> itemsToDelete = baseMapper.selectByIds(ids);
        Set<Long> orderIds = new HashSet<>();

        if (isValid) {
            // 校验采购订单明细是否可以删除
            for (PurchaseOrderItem item : itemsToDelete) {
                orderIds.add(item.getOrderId());

                // 检查关联的采购订单状态
                PurchaseOrder order = purchaseOrderMapper.selectById(item.getOrderId());
                if (order != null && PurchaseOrderStatus.DRAFT != order.getOrderStatus()) {
                    throw new ServiceException("采购订单明细所属订单【" + order.getOrderCode() + "】状态为【" +
                        order.getOrderStatus() + "】，不允许删除明细");
                }

                // 检查是否已有收货记录
                if (item.getFinishQuantity() != null && item.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    throw new ServiceException("采购订单明细【" + item.getProductName() + "】已有收货记录，不允许删除");
                }

                log.info("删除采购订单明细校验通过：产品【{}】", item.getProductName());
            }
        } else {
            // 收集订单ID用于后续金额更新
            orderIds.addAll(itemsToDelete.stream().map(PurchaseOrderItem::getOrderId).collect(Collectors.toSet()));
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            boolean flag = result > 0;

            // ✅ 明细删除后自动更新主订单金额合计
            if (flag) {
                for (Long orderId : orderIds) {
                    updateOrderTotalAmounts(orderId);
                }
                log.info("批量删除采购订单明细成功，删除数量：{}", result);
            }

            return flag;
        } catch (Exception e) {
            log.error("批量删除采购订单明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购订单明细失败：" + e.getMessage());
        }
    }

    /**
     * 批量新增或更新采购订单明细
     *
     * @param items 采购订单明细列表
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<PurchaseOrderItemBo> items) {
        try {
            List<PurchaseOrderItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(fillRedundantFields(bo), PurchaseOrderItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新采购明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新采购明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据订单ID查询明细列表
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    @Override
    public List<PurchaseOrderItemVo> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<PurchaseOrderItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseOrderItem::getOrderId, orderId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 填充冗余信息
     *
     * @param bo 采购明细
     * @return 采购明细
     */
    private PurchaseOrderItemBo fillRedundantFields(PurchaseOrderItemBo bo) {
        // 填充产品信息
        if (bo.getProductId() != null) {
            ProductVo product = productService.queryById(bo.getProductId());
            if (product != null) {
                bo.setProductCode(product.getProductCode());
                bo.setProductName(product.getProductName());
                bo.setUnitId(product.getUnitId());
                bo.setUnitCode(product.getUnitCode());
                bo.setUnitName(product.getUnitName());
                // 如果没有设置价格，使用产品的销售价格
                if (bo.getPrice() == null && product.getPurchasePrice() != null) {
                    bo.setPrice(product.getPurchasePrice());
                }
                if (bo.getPriceExclusiveTax() == null && product.getPurchasePriceExclusiveTax() != null) {
                    bo.setPriceExclusiveTax(product.getPurchasePriceExclusiveTax());
                }
                if (bo.getTaxRate() == null && product.getPurchaseTaxRate() != null) {
                    bo.setTaxRate(product.getPurchaseTaxRate());
                }
            }
        }
        // 填充价格信息
        if (bo.getPrice() != null && bo.getTaxRate() != null && bo.getQuantity() != null) {
            // 从含税价计算其他价格字段
            TaxCalculationResultBo result = TaxCalculationUtils.calculate(bo.getQuantity(), bo.getTaxRate(), bo.getPrice());
            bo.setPriceExclusiveTax(result.getPriceExclusiveTax());
            bo.setAmount(result.getAmount());
            bo.setAmountExclusiveTax(result.getAmountExclusiveTax());
            bo.setTaxAmount(result.getTaxAmount());

            log.info("采购入库明细价格计算完成 - 数量: {}, 含税价: {}, 税率: {}%, 不含税价: {}, 金额(含税): {}, 金额(不含税): {}, 税额: {}",
                bo.getQuantity(), bo.getPrice(), bo.getTaxRate(), bo.getPriceExclusiveTax(), bo.getAmount(), bo.getAmountExclusiveTax(), bo.getTaxAmount());
        }
        return bo;
    }

    /**
     * 更新主订单金额合计
     * ✅ 明细变更后自动更新主订单的价税分离金额字段
     *
     * @param orderId 订单ID
     */
    private void updateOrderTotalAmounts(Long orderId) {
        try {
            if (orderId == null) {
                log.warn("订单ID为空，跳过金额合计更新");
                return;
            }

            // ✅ 使用标准的金额合计方法
            TaxCalculationResultBo totalAmount = baseMapper.calculateTotalAmount(orderId);

            if (totalAmount == null) {
                log.debug("采购订单【{}】没有明细，清空金额字段", orderId);
                totalAmount = TaxCalculationResultBo.builder()
                    .amount(BigDecimal.ZERO)
                    .amountExclusiveTax(BigDecimal.ZERO)
                    .taxAmount(BigDecimal.ZERO)
                    .build();
            }

            // 更新主订单金额字段
            PurchaseOrder update = new PurchaseOrder();
            update.setOrderId(orderId);
            update.setAmount(totalAmount.getAmount());
            update.setAmountExclusiveTax(totalAmount.getAmountExclusiveTax());
            update.setTaxAmount(totalAmount.getTaxAmount());

            int result = purchaseOrderMapper.updateById(update);
            if (result > 0) {
                log.debug("更新采购订单金额合计成功 - 订单ID: {}, 含税金额: {}, 不含税金额: {}, 税额: {}",
                    orderId, totalAmount.getAmount(), totalAmount.getAmountExclusiveTax(), totalAmount.getTaxAmount());
            } else {
                log.warn("更新采购订单金额合计失败 - 订单ID: {}", orderId);
            }
        } catch (Exception e) {
            log.error("更新采购订单金额合计异常 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

}
