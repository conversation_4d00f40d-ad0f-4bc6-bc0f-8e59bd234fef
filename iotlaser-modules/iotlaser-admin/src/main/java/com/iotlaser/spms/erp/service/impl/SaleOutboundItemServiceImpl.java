package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import com.iotlaser.spms.erp.mapper.SaleOutboundItemMapper;
import com.iotlaser.spms.erp.mapper.SaleOutboundMapper;
import com.iotlaser.spms.erp.service.ISaleOutboundItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售出库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOutboundItemServiceImpl implements ISaleOutboundItemService {

    private final SaleOutboundItemMapper baseMapper;
    private final SaleOutboundMapper saleOutboundMapper;  // ✅ 用于更新主出库单金额
    private final ILocationService locationService;

    /**
     * 查询销售出库明细
     *
     * @param itemId 主键
     * @return 销售出库明细
     */
    @Override
    public SaleOutboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询销售出库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库明细分页列表
     */
    @Override
    public TableDataInfo<SaleOutboundItemVo> queryPageList(SaleOutboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOutboundItem> lqw = buildQueryWrapper(bo);
        Page<SaleOutboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售出库明细列表
     *
     * @param bo 查询条件
     * @return 销售出库明细列表
     */
    @Override
    public List<SaleOutboundItemVo> queryList(SaleOutboundItemBo bo) {
        LambdaQueryWrapper<SaleOutboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOutboundItem> buildQueryWrapper(SaleOutboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOutboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOutboundItem::getItemId);
        lqw.eq(bo.getOutboundId() != null, SaleOutboundItem::getOutboundId, bo.getOutboundId());
        lqw.eq(bo.getProductId() != null, SaleOutboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleOutboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleOutboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, SaleOutboundItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), SaleOutboundItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), SaleOutboundItem::getUnitName, bo.getUnitName());
        lqw.eq(bo.getQuantity() != null, SaleOutboundItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, SaleOutboundItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getPrice() != null, SaleOutboundItem::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOutboundItem::getStatus, bo.getStatus());
        //lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), SaleOutboundItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * 新增销售出库明细
     *
     * @param bo 销售出库明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SaleOutboundItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        SaleOutboundItem add = MapstructUtils.convert(bo, SaleOutboundItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());

            // ✅ 明细保存后自动更新主出库单金额合计
            if (add.getOutboundId() != null) {
                updateOutboundTotalAmounts(add.getOutboundId());
            }
        }
        return flag;
    }

    /**
     * 修改销售出库明细
     *
     * @param bo 销售出库明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SaleOutboundItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        SaleOutboundItem update = MapstructUtils.convert(bo, SaleOutboundItem.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;

        // ✅ 明细更新后自动更新主出库单金额合计
        if (flag && update.getOutboundId() != null) {
            updateOutboundTotalAmounts(update.getOutboundId());
        }

        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOutboundItem entity) {
        // 校验同一出库单中产品不能重复
        if (entity.getOutboundId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<SaleOutboundItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOutboundItem::getOutboundId, entity.getOutboundId());
            wrapper.eq(SaleOutboundItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(SaleOutboundItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一销售出库单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除销售出库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 获取要删除的明细，用于后续更新主出库单金额
        List<SaleOutboundItem> itemsToDelete = baseMapper.selectByIds(ids);
        Set<Long> outboundIds = new HashSet<>();

        if (isValid) {
            // 校验明细是否可以删除
            for (SaleOutboundItem item : itemsToDelete) {
                outboundIds.add(item.getOutboundId());
                log.info("删除销售出库明细，产品：{}", item.getProductName());
            }
        } else {
            // 收集出库单ID用于后续金额更新
            outboundIds.addAll(itemsToDelete.stream().map(SaleOutboundItem::getOutboundId).collect(Collectors.toSet()));
        }

        boolean flag = baseMapper.deleteByIds(ids) > 0;

        // ✅ 明细删除后自动更新主出库单金额合计
        if (flag) {
            for (Long outboundId : outboundIds) {
                updateOutboundTotalAmounts(outboundId);
            }
        }

        return flag;
    }

    /**
     * 批量插入或更新销售出库明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<SaleOutboundItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<SaleOutboundItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, SaleOutboundItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售出库明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(SaleOutboundItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }

    /**
     * 更新主出库单金额合计
     * ✅ 明细变更后自动更新主出库单的价税分离金额字段
     *
     * @param outboundId 出库单ID
     */
    private void updateOutboundTotalAmounts(Long outboundId) {
        try {
            if (outboundId == null) {
                log.warn("出库单ID为空，跳过金额合计更新");
                return;
            }

            // ✅ 使用标准的金额合计方法
            TaxCalculationResultBo totalAmount = baseMapper.calculateTotalAmount(outboundId);

            if (totalAmount == null) {
                log.debug("销售出库单【{}】没有明细，清空金额字段", outboundId);
                totalAmount = TaxCalculationResultBo.builder()
                    .amount(BigDecimal.ZERO)
                    .amountExclusiveTax(BigDecimal.ZERO)
                    .taxAmount(BigDecimal.ZERO)
                    .build();
            }

            // 更新主出库单金额字段
            SaleOutbound update = new SaleOutbound();
            update.setOutboundId(outboundId);
            update.setAmount(totalAmount.getAmount());
            update.setAmountExclusiveTax(totalAmount.getAmountExclusiveTax());
            update.setTaxAmount(totalAmount.getTaxAmount());

            int result = saleOutboundMapper.updateById(update);
            if (result > 0) {
                log.debug("更新销售出库单金额合计成功 - 出库单ID: {}, 含税金额: {}, 不含税金额: {}, 税额: {}",
                    outboundId, totalAmount.getAmount(), totalAmount.getAmountExclusiveTax(), totalAmount.getTaxAmount());
            } else {
                log.warn("更新销售出库单金额合计失败 - 出库单ID: {}", outboundId);
            }
        } catch (Exception e) {
            log.error("更新销售出库单金额合计异常 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

}
