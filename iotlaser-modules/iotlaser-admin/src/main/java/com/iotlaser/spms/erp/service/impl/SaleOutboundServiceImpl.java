package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.enums.GenCodeType;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.SaleOutbound;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundBo;
import com.iotlaser.spms.erp.domain.bo.SaleOutboundItemBo;
import com.iotlaser.spms.erp.domain.vo.*;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.mapper.SaleOutboundItemMapper;
import com.iotlaser.spms.erp.mapper.SaleOutboundMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryStatus;
import com.iotlaser.spms.wms.service.IInventoryService;
import com.iotlaser.spms.wms.service.IOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 销售出库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-05-10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOutboundServiceImpl implements ISaleOutboundService {

    private final SaleOutboundMapper baseMapper;
    private final SaleOutboundItemMapper itemMapper;
    private final ISaleReturnService saleReturnService;
    private final IOutboundService outboundService;
    private final IInventoryService inventoryService;
    private final IFinArReceivableService finArReceivableService;
    private final IFinArReceiptReceivableLinkService finArReceiptReceivableLinkService;
    private final IFinAccountLedgerService finAccountLedgerService;
    private final ICompanyService companyService;
    private final IProductService productService;
    private final Gen gen;
    @Lazy
    @Autowired
    private ISaleOrderService saleOrderService;

    /**
     * 查询销售出库
     *
     * @param outboundId 主键
     * @return 销售出库
     */
    @Override
    public SaleOutboundVo queryById(Long outboundId) {
        return baseMapper.selectVoById(outboundId);
    }

    /**
     * 分页查询销售出库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库分页列表
     */
    @Override
    public TableDataInfo<SaleOutboundVo> queryPageList(SaleOutboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOutbound> lqw = buildQueryWrapper(bo);
        Page<SaleOutboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售出库列表
     *
     * @param bo 查询条件
     * @return 销售出库列表
     */
    @Override
    public List<SaleOutboundVo> queryList(SaleOutboundBo bo) {
        LambdaQueryWrapper<SaleOutbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据销售订单ID查询销售出库单列表
     *
     * @param orderId 销售订单ID
     * @return 销售出库单列表
     */
    @Override
    public List<SaleOutboundVo> queryByOrderId(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("销售订单ID不能为空");
        }
        return baseMapper.selectListByDirectSourceId(orderId);
    }

    private LambdaQueryWrapper<SaleOutbound> buildQueryWrapper(SaleOutboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOutbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOutbound::getOutboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getOutboundCode()), SaleOutbound::getOutboundCode, bo.getOutboundCode());
        lqw.eq(bo.getSourceId() != null, SaleOutbound::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), SaleOutbound::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(SaleOutbound::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, SaleOutbound::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), SaleOutbound::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(SaleOutbound::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getCustomerId() != null, SaleOutbound::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleOutbound::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getOutboundTime() != null, SaleOutbound::getOutboundTime, bo.getOutboundTime());
        if (bo.getOutboundStatus() != null) {
            lqw.eq(SaleOutbound::getOutboundStatus, bo.getOutboundStatus().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOutbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            SaleOutbound::getOutboundTime, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * 新增销售出库
     *
     * @param bo 销售出库
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOutboundVo insertByBo(SaleOutboundBo bo) {
        try {
            // 生成出库单编号
            if (StringUtils.isBlank(bo.getOutboundCode())) {
                bo.setOutboundCode(gen.code(GenCodeType.ERP_SALE_OUTBOUND_CODE));
            }

            // 设置初始状态
            if (bo.getOutboundStatus() == null) {
                bo.setOutboundStatus(SaleOutboundStatus.DRAFT);
            }

            // 设置出库时间
            if (bo.getOutboundTime() == null) {
                bo.setOutboundTime(LocalDateTime.now());
            }

            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 转换为实体并校验
            SaleOutbound add = MapstructUtils.convert(bo, SaleOutbound.class);
            validEntityBeforeSave(add);

            // 插入主表
            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                throw new ServiceException("新增销售出库失败");
            }

            bo.setOutboundId(add.getOutboundId());

            log.info("新增销售出库成功：{}", add.getOutboundCode());
            return MapstructUtils.convert(add, SaleOutboundVo.class);
        } catch (Exception e) {
            log.error("新增销售出库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增销售出库失败：" + e.getMessage());
        }
    }

    /**
     * 修改销售出库
     *
     * @param bo 销售出库
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOutboundVo updateByBo(SaleOutboundBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 转换为实体并校验
            SaleOutbound update = MapstructUtils.convert(bo, SaleOutbound.class);
            validEntityBeforeSave(update);

            // 更新主表
            boolean result = baseMapper.updateById(update) > 0;
            if (!result) {
                throw new ServiceException("修改销售出库失败");
            }

            log.info("修改销售出库成功：{}", update.getOutboundCode());
            return MapstructUtils.convert(update, SaleOutboundVo.class);
        } catch (Exception e) {
            log.error("修改销售出库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改销售出库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOutbound entity) {
        // 校验出库单编码唯一性
        if (StringUtils.isNotBlank(entity.getOutboundCode())) {
            LambdaQueryWrapper<SaleOutbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOutbound::getOutboundCode, entity.getOutboundCode());
            if (entity.getOutboundId() != null) {
                wrapper.ne(SaleOutbound::getOutboundId, entity.getOutboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("出库单编码已存在：" + entity.getOutboundCode());
            }
        }
    }

    /**
     * 校验并批量删除销售出库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验出库单状态，只有草稿状态的出库单才能删除
            List<SaleOutbound> outbounds = baseMapper.selectByIds(ids);
            for (SaleOutbound outbound : outbounds) {
                if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()) {
                    throw new ServiceException("出库单[" + outbound.getOutboundCode() + "]不是草稿状态，无法删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 确认销售出库单
     *
     * @param outboundId 出库单ID
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOutbound(Long outboundId) {
        SaleOutbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 校验出库单状态
        if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()) {
            throw new ServiceException("只有草稿状态的出库单才能确认");
        }

        List<SaleOutboundItemVo> items = itemMapper.queryByOutboundId(outboundId);
        if (items.isEmpty()) {
            throw new ServiceException("出库明细不能为空");
        }

        // 更新出库单状态
        outbound.setOutboundStatus(SaleOutboundStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(outbound) > 0;

        // ✅ [提交出库单后，触发WMS任务] - 已完善
        // 确认成功后，调用WMS模块创建出库执行单
        if (result) {
            try {
                // 创建完整的出库单VO，包含明细信息
                SaleOutboundVo outboundVo = MapstructUtils.convert(outbound, SaleOutboundVo.class);
                outboundVo.setItems(items);

                // 调用WMS服务创建出库执行单
                Boolean wmsResult = outboundService.createFromSaleOutbound(outboundVo);

                if (wmsResult) {
                    log.info("销售出库单【{}】成功创建WMS出库执行单", outbound.getOutboundCode());
                } else {
                    log.error("销售出库单【{}】创建WMS出库执行单失败", outbound.getOutboundCode());
                    // 不抛出异常，允许ERP出库单确认成功，WMS任务可以后续重试
                }
            } catch (Exception e) {
                log.error("销售出库单【{}】创建WMS出库执行单异常: {}", outbound.getOutboundCode(), e.getMessage(), e);
                // 不抛出异常，避免影响ERP出库单确认流程
            }
        }

        return result;
    }

    /**
     * 批量确认销售出库单
     *
     * @param outboundIds 出库单ID集合
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchConfirmOutbounds(Collection<Long> outboundIds) {
        for (Long outboundId : outboundIds) {
            confirmOutbound(outboundId);
        }
        return true;
    }

    /**
     * 取消销售出库单
     *
     * @param outboundId 出库单ID
     * @param reason     取消原因
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOutbound(Long outboundId, String reason) {
        SaleOutbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 校验出库单状态，只有草稿和待出库状态的出库单才能取消
        if (SaleOutboundStatus.DRAFT != outbound.getOutboundStatus()
            && SaleOutboundStatus.PENDING_WAREHOUSE != outbound.getOutboundStatus()) {
            throw new ServiceException("只有草稿或待出库状态的出库单才能取消");
        }

        try {
            // 更新出库单状态和取消原因
            if (StringUtils.isNotBlank(reason)) {
                String currentRemark = StringUtils.isBlank(outbound.getRemark()) ? "" : outbound.getRemark();
                outbound.setRemark(currentRemark + " [取消原因：" + reason + "]");
            }

            // 设置为取消状态
            outbound.setOutboundStatus(SaleOutboundStatus.CANCELLED);

            boolean result = baseMapper.updateById(outbound) > 0;
            if (result) {
                log.info("销售出库单【{}】取消成功，原因：{}", outbound.getOutboundCode(), reason);
            }
            return result;
        } catch (Exception e) {
            log.error("销售出库单【{}】取消失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("取消出库单失败：" + e.getMessage());
        }
    }

    /**
     * 完成销售出库
     *
     * @param outboundId 出库单ID
     * @return 是否完成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeOutbound(Long outboundId) {
        SaleOutbound outbound = baseMapper.selectById(outboundId);
        if (outbound == null) {
            throw new ServiceException("销售出库单不存在");
        }

        // 校验出库单状态
        if (SaleOutboundStatus.PENDING_WAREHOUSE != outbound.getOutboundStatus()) {
            throw new ServiceException("只有待出库状态的出库单才能完成");
        }

        try {
            // 检查库存可用性
            checkInventoryAvailability(outboundId);

            // 更新出库单状态
            outbound.setOutboundStatus(SaleOutboundStatus.COMPLETED);
            outbound.setOutboundTime(LocalDateTime.now());

            boolean result = baseMapper.updateById(outbound) > 0;
            if (result) {
                log.info("销售出库单【{}】完成出库", outbound.getOutboundCode());
                // 处理库存扣减逻辑
                processInventoryDeduction(outbound);

                // TODO: [业财一体化关键节点：出库完成后，触发应收] - 参考文档 docs/design/README_FINANCE.md
                // 出库完成后，必须调用财务模块的IFinArReceivableService，自动为该笔发货创建应收单。
                // SaleOutboundVo outboundVo = queryById(outboundId); // 查询包含完整信息的Vo
                // finArReceivableService.createFromSaleOutbound(outboundVo);
            }
            return result;
        } catch (Exception e) {
            log.error("销售出库单【{}】完成失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("完成出库失败：" + e.getMessage());
        }
    }

    /**
     * 创建仓库出库单
     *
     * @param outboundId 出库单ID
     * @return 是否创建成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOutbound(Long outboundId) {
        try {
            if (outboundId == null) {
                throw new ServiceException("销售出库单ID不能为空");
            }
            // 检查订单是否存在
            SaleOutboundVo outboundVo = baseMapper.selectVoById(outboundId);
            if (outboundVo == null) {
                throw new ServiceException("销售出库单不存在");
            }
            // 检查出库单状态
            if (SaleOutboundStatus.PENDING_WAREHOUSE != outboundVo.getOutboundStatus()) {
                throw new ServiceException("只有待出库的销售出库单才能创建仓库出库单");
            }
            List<SaleOutboundItemVo> items = itemMapper.queryByOutboundId(outboundId);
            if (items.isEmpty()) {
                throw new ServiceException("销售出库单没有明细，不能创建仓库出库单");
            }

            outboundVo.setItems(items);
            // 调用出库单服务创建出库单
            boolean result = outboundService.createFromSaleOutbound(outboundVo);
            if (!result) {
                throw new ServiceException("创建销售出库单失败");
            }
            log.info("创建销售出库单成功 - 销售出库单: {}", outboundVo.getOutboundCode());
            return true;
        } catch (Exception e) {
            log.error("创建销售出库单失败 - 销售出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("创建销售出库单失败：" + e.getMessage());
        }
    }

    /**
     * 创建销售退货单
     *
     * @param outboundId 出库单ID
     * @return 是否创建成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReturn(Long outboundId) {
        try {
            if (outboundId == null) {
                throw new ServiceException("销售出库单ID不能为空");
            }
            // 检查订单是否存在
            SaleOutboundVo outboundVo = baseMapper.selectVoById(outboundId);
            if (outboundVo == null) {
                throw new ServiceException("销售出库单不存在");
            }
            // 检查出库单状态
            if (SaleOutboundStatus.COMPLETED != outboundVo.getOutboundStatus()) {
                throw new ServiceException("只有已完成的销售出库单才能创建退货单");
            }
            List<SaleOutboundItemVo> items = itemMapper.queryByOutboundId(outboundId);
            if (items.isEmpty()) {
                throw new ServiceException("销售出库单没有明细，不能创建退货单");
            }
            // 检查是否已有退货单
            if (saleReturnService.existsByDirectSourceId(outboundId)) {
                throw new ServiceException("该销售出库单已有退货单，不能重复创建");
            }
            outboundVo.setItems(items);
            // 调用出库单服务创建出库单
            boolean result = saleReturnService.createFromSaleOutbound(outboundVo);
            if (!result) {
                throw new ServiceException("创建销售出库单失败");
            }
            log.info("创建销售出库单成功 - 销售出库单: {}", outboundVo.getOutboundCode());
            return true;
        } catch (Exception e) {
            log.error("创建销售出库单失败 - 销售出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("创建销售出库单失败：" + e.getMessage());
        }
    }

    /**
     * 根据销售订单创建出库单
     *
     * @param orderVo 销售订单
     * @return 创建的出库单
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFromSaleOrder(SaleOrderVo orderVo) {
        // 创建出库单
        SaleOutbound add = new SaleOutbound();

        add.setOutboundCode(gen.code(GenCodeType.ERP_SALE_OUTBOUND_CODE));

        add.setSourceId(orderVo.getSourceId());
        add.setSourceCode(orderVo.getSourceCode());
        add.setSourceType(orderVo.getSourceType());

        add.setDirectSourceId(orderVo.getOrderId());
        add.setDirectSourceCode(orderVo.getOrderCode());
        add.setDirectSourceType(DirectSourceType.SALE_ORDER);

        add.setCustomerId(orderVo.getCustomerId());
        add.setCustomerName(orderVo.getCustomerName());

        add.setOutboundStatus(SaleOutboundStatus.DRAFT);
        add.setOutboundTime(LocalDateTime.now());
        add.setSummary("由销售订单[" + orderVo.getOrderCode() + "]创建");

        // 插入出库单
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("创建销售出库单失败");
        }

        if (orderVo.getItems() != null && !orderVo.getItems().isEmpty()) {
            List<SaleOutboundItem> outboundItems = new ArrayList<>();
            for (SaleOrderItemVo orderItem : orderVo.getItems()) {
                SaleOutboundItem outboundItem = new SaleOutboundItem();
                outboundItem.setOutboundId(add.getOutboundId());
                outboundItem.setProductId(orderItem.getProductId());
                outboundItem.setProductCode(orderItem.getProductCode());
                outboundItem.setProductName(orderItem.getProductName());
                outboundItem.setUnitId(orderItem.getUnitId());
                outboundItem.setUnitCode(orderItem.getUnitCode());
                outboundItem.setUnitName(orderItem.getUnitName());
                outboundItem.setQuantity(orderItem.getQuantity());
                outboundItem.setPrice(orderItem.getPrice());
                outboundItem.setPriceExclusiveTax(orderItem.getPriceExclusiveTax());
                outboundItem.setAmount(orderItem.getAmount());
                outboundItem.setAmountExclusiveTax(orderItem.getAmountExclusiveTax());
                outboundItem.setTaxRate(orderItem.getTaxRate());
                outboundItem.setTaxAmount(orderItem.getTaxAmount());
                outboundItem.setRemark("由销售订单[" + orderVo.getOrderCode() + "]创建");
                outboundItems.add(outboundItem);
            }

            boolean insertItem = itemMapper.insertBatch(outboundItems);
            if (!insertItem) {
                throw new ServiceException("基于采购入库单创建采购退货单失败");
            }
        }
        log.info("基于销售订单【{}】创建销售出库单【{}】成功", orderVo.getOrderCode(), add.getOutboundCode());
        return true;
    }

    /**
     * 检查是否有关联的出库单
     *
     * @param orderId 销售订单ID
     * @return 是否存在
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean existsByOrderId(Long orderId) {
        return baseMapper.existsByDirectSourceId(orderId);
    }

    /**
     * 处理库存扣减逻辑
     *
     * @param outbound 出库单
     */
    private void processInventoryDeduction(SaleOutbound outbound) {
        try {
            // 获取出库明细
            List<SaleOutboundItemVo> items = itemMapper.queryByOutboundId(outbound.getOutboundId());

            for (SaleOutboundItemVo item : items) {
                // ✅ 调用WMS模块的库存扣减服务
                boolean deductResult = inventoryService.adjustBatch(
                    item.getProductId(),
                    item.getLocationId(),
                    item.getQuantity().negate(), // 负数表示扣减
                    "销售出库扣减 - 出库单：" + outbound.getOutboundCode(),
                    outbound.getHandlerId(),
                    outbound.getHandlerName()
                );

                if (!deductResult) {
                    throw new ServiceException("产品【" + item.getProductName() + "】库存扣减失败");
                }

                log.info("销售出库库存扣减成功：产品【{}】数量【{}】库位【{}】",
                    item.getProductName(), item.getQuantity(), item.getLocationCode());

                // 处理批次库存扣减（FIFO原则）
                if (item.getProductId() != null) {
                    processBatchInventoryDeduction(item, outbound);
                }
            }

            log.info("销售出库单【{}】库存扣减处理完成", outbound.getOutboundCode());
        } catch (Exception e) {
            log.error("销售出库单【{}】库存扣减失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("库存扣减失败：" + e.getMessage());
        }
    }

    /**
     * 处理批次库存扣减（FIFO原则）
     *
     * @param item     出库明细
     * @param outbound 出库单
     */
    private void processBatchInventoryDeduction(SaleOutboundItemVo item, SaleOutbound outbound) {
        try {
            // 获取该产品在该库位的所有可用批次（按先进先出排序）
            List<InventoryVo> availableBatches = inventoryService.getExpiringBatches(
                365, item.getProductId(), item.getLocationId());

            BigDecimal remainingQty = item.getQuantity();

            for (InventoryVo batch : availableBatches) {
                if (remainingQty.compareTo(BigDecimal.ZERO) <= 0) {
                    break; // 已扣减完毕
                }

                // 检查批次可用数量
                // TODO: Inventory实体中没有allocatedQuantity字段，需要重新设计库存分配逻辑
                // 暂时使用全部数量作为可用数量，待实体完善后修正
                BigDecimal batchAvailableQty = batch.getQuantity();
                // 原逻辑（待实体完善后启用）：
                // BigDecimal batchAvailableQty = batch.getQuantity().subtract(
                //     batch.getAllocatedQuantity() != null ? batch.getAllocatedQuantity() : BigDecimal.ZERO);

                if (batchAvailableQty.compareTo(BigDecimal.ZERO) <= 0) {
                    continue; // 该批次无可用库存
                }

                // 计算本批次扣减数量
                BigDecimal deductQty = remainingQty.min(batchAvailableQty);

                // 扣减批次库存
                // TODO: 实现批次库存扣减逻辑
                // inventoryService.deductBatchQuantity(batch.getBatchId(), deductQty);

                remainingQty = remainingQty.subtract(deductQty);

                log.info("批次库存扣减：批次【{}】扣减数量【{}】剩余需扣减【{}】",
                    batch.getInternalBatchNumber(), deductQty, remainingQty);
            }

            if (remainingQty.compareTo(BigDecimal.ZERO) > 0) {
                log.warn("产品【{}】批次库存不足，未扣减数量：{}", item.getProductName(), remainingQty);
            }
        } catch (Exception e) {
            log.error("批次库存扣减失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 检查库存可用性
     * 重要功能 - 库存不足检查，防止超卖
     *
     * @param outboundId 出库单ID
     */
    private void checkInventoryAvailability(Long outboundId) {
        try {
            // 获取出库明细
            List<SaleOutboundItemVo> items = itemMapper.queryByOutboundId(outboundId);

            List<String> insufficientItems = new ArrayList<>();

            for (SaleOutboundItemVo item : items) {
                // 查询产品在指定库位的可用库存
                BigDecimal availableQty = inventoryService.getAvailableQuantity(
                    item.getProductId(), item.getLocationId());

                // 检查库存数量是否足够
                if (availableQty.compareTo(item.getQuantity()) < 0) {
                    String insufficientInfo = String.format("产品【%s】库存不足，需要数量：%s，可用数量：%s",
                        item.getProductName(), item.getQuantity(), availableQty);
                    insufficientItems.add(insufficientInfo);
                    log.warn(insufficientInfo);
                } else {
                    log.info("产品【{}】库存检查通过，需要数量：{}，可用数量：{}",
                        item.getProductName(), item.getQuantity(), availableQty);
                }

                // 检查批次库存可用性
                checkBatchInventoryAvailability(item);
            }

            // 如果有库存不足的产品，抛出异常
            if (!insufficientItems.isEmpty()) {
                String errorMessage = "库存不足，无法完成出库：\n" + String.join("\n", insufficientItems);
                throw new ServiceException(errorMessage);
            }

            log.info("出库单【{}】库存检查通过", outboundId);
        } catch (Exception e) {
            log.error("出库单【{}】库存检查失败：{}", outboundId, e.getMessage(), e);
            throw new ServiceException("库存检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查批次库存可用性
     *
     * @param item 出库明细
     */
    private void checkBatchInventoryAvailability(SaleOutboundItemVo item) {
        try {
            // 获取该产品在该库位的所有可用批次
            List<InventoryVo> availableBatches = inventoryService.getExpiringBatches(
                365, item.getProductId(), item.getLocationId());

            // 计算批次总可用数量
            BigDecimal totalBatchAvailableQty = BigDecimal.ZERO;
            int expiredBatchCount = 0;
            int warningBatchCount = 0;

            for (InventoryVo batch : availableBatches) {
                // 检查批次状态
                if (InventoryStatus.EXPIRED == batch.getInventoryStatus()) {
                    expiredBatchCount++;
                    continue;
                }
                if (InventoryStatus.WARNING == batch.getInventoryStatus()) {
                    warningBatchCount++;
                }

                // 计算批次可用数量
                // TODO: Inventory实体中没有allocatedQuantity字段，需要重新设计库存分配逻辑
                // 暂时使用全部数量作为可用数量，待实体完善后修正
                BigDecimal batchAvailableQty = batch.getQuantity();
                // 原逻辑（待实体完善后启用）：
                // BigDecimal batchAvailableQty = batch.getQuantity().subtract(
                //     batch.getAllocatedQuantity() != null ? batch.getAllocatedQuantity() : BigDecimal.ZERO);

                if (batchAvailableQty.compareTo(BigDecimal.ZERO) > 0) {
                    totalBatchAvailableQty = totalBatchAvailableQty.add(batchAvailableQty);
                }
            }

            // 检查批次库存是否足够
            if (totalBatchAvailableQty.compareTo(item.getQuantity()) < 0) {
                log.warn("产品【{}】批次库存不足，需要数量：{}，批次可用数量：{}",
                    item.getProductName(), item.getQuantity(), totalBatchAvailableQty);
            }

            // 记录批次状态信息
            if (expiredBatchCount > 0) {
                log.warn("产品【{}】存在{}个过期批次", item.getProductName(), expiredBatchCount);
            }
            if (warningBatchCount > 0) {
                log.warn("产品【{}】存在{}个即将过期批次", item.getProductName(), warningBatchCount);
            }

        } catch (Exception e) {
            log.error("批次库存检查失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从销售出库单生成应收账款
     *
     * @param outboundId     出库单ID
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 应收账款ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateArReceivableFromOutbound(Long outboundId, String receivableType,
                                                 LocalDate dueDate, String paymentTerms,
                                                 Long operatorId, String operatorName) {
        try {
            // 获取出库单信息
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("销售出库单不存在");
            }

            // 校验出库单状态
            if (SaleOutboundStatus.COMPLETED != outbound.getOutboundStatus()) {
                throw new ServiceException("只有已完成的出库单才能生成应收账款");
            }

            // 检查是否已经生成过应收账款
            if (finArReceivableService.existsByOutboundId(outboundId)) {
                throw new ServiceException("该出库单已生成应收账款，不能重复生成");
            }

            // 获取出库明细
            List<SaleOutboundItemVo> outboundItems = itemMapper.queryByOutboundId(outboundId);
            if (outboundItems.isEmpty()) {
                throw new ServiceException("出库单没有明细，无法生成应收账款");
            }

            // 调用财务服务生成应收账款
            Long receivableId = finArReceivableService.generateFromSaleOutbound(outboundId,
                receivableType, dueDate, paymentTerms, operatorId, operatorName);

            // 更新出库单状态，标记已生成应收
            // 注意：这里需要确保SaleOutboundStatus枚举中有RECEIVABLE_GENERATED状态
            // 暂时使用备注字段记录
            String currentRemark = StringUtils.isBlank(outbound.getRemark()) ? "" : outbound.getRemark();
            outbound.setRemark(currentRemark + " [已生成应收账款ID:" + receivableId + "]");
            baseMapper.updateById(outbound);

            log.info("从销售出库单生成应收账款成功 - 出库单: {}, 应收账款ID: {}, 操作人: {}",
                outbound.getOutboundCode(), receivableId, operatorName);

            return receivableId;
        } catch (Exception e) {
            log.error("从销售出库单生成应收账款失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 批量从出库单生成应收账款
     *
     * @param outboundIds    出库单ID列表
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 批量生成结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateArReceivables(List<Long> outboundIds, String receivableType,
                                                          LocalDate dueDate, String paymentTerms,
                                                          Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (Long outboundId : outboundIds) {
                try {
                    Long receivableId = generateArReceivableFromOutbound(outboundId, receivableType,
                        dueDate, paymentTerms, operatorId, operatorName);

                    successList.add(Map.of(
                        "outboundId", outboundId,
                        "receivableId", receivableId,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "outboundId", outboundId,
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", outboundIds.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量生成应收账款完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                outboundIds.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量生成应收账款失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量生成应收账款失败：" + e.getMessage());
        }
    }

    /**
     * 出库完成后自动生成应收单
     *
     * @param outboundId   出库单ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateReceivableAfterOutboundComplete(Long outboundId, Long operatorId, String operatorName) {
        try {
            if (outboundId == null) {
                throw new ServiceException("出库单ID不能为空");
            }

            // 校验出库单状态为已完成
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("出库单不存在");
            }

            if (SaleOutboundStatus.COMPLETED != outbound.getOutboundStatus()) {
                throw new ServiceException("只有已完成的出库单才能生成应收单");
            }

            // 检查是否已生成应收单
            if (finArReceivableService.existsByOutboundId(outboundId)) {
                throw new ServiceException("该出库单已生成应收单，不能重复生成");
            }

            log.info("开始从出库单生成应收单 - 出库单ID: {}, 出库单编号: {}, 操作人: {}",
                outboundId, outbound.getOutboundCode(), operatorName);

            // 从出库单信息生成应收单主表
            Long receivableId = finArReceivableService.generateFromSaleOutbound(
                outboundId,
                outbound.getDirectSourceId(),
                outbound.getCustomerId(),
                operatorId,
                operatorName
            );

            if (receivableId == null) {
                throw new ServiceException("应收单生成失败");
            }

            // 从出库单明细生成应收单明细
            List<SaleOutboundItemVo> outboundItems = itemMapper.queryByOutboundId(outboundId);

            if (!outboundItems.isEmpty()) {
                List<Long> outboundItemIds = outboundItems.stream()
                    .map(SaleOutboundItemVo::getItemId)
                    .toList();

                Boolean itemsResult = finArReceivableService.generateReceivableItemsFromOutboundItems(
                    receivableId, outboundItemIds, operatorId, operatorName);

                if (!itemsResult) {
                    throw new ServiceException("应收单明细生成失败");
                }
            }

            log.info("从出库单生成应收单成功 - 出库单: {}, 应收单ID: {}, 操作人: {}",
                outbound.getOutboundCode(), receivableId, operatorName);

            return true;
        } catch (Exception e) {
            log.error("从出库单生成应收单失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("生成应收单失败：" + e.getMessage());
        }
    }

    /**
     * 销售业务完整流程：从出库完成到收款入账
     *
     * @param outboundId    出库单ID
     * @param receiptAmount 收款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 完整的业务结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completeSaleBusinessFlow(Long outboundId, BigDecimal receiptAmount,
                                                        Long accountId, Long operatorId, String operatorName) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行销售业务完整流程 - 出库单ID: {}, 收款金额: {}, 操作人: {}",
                outboundId, receiptAmount, operatorName);

            // ✅ [销售出库完成后的业务流程优化] - 已完善
            // 已实现的优化功能：
            // 1. ✅ 支持部分收款：允许收款金额小于应收金额，自动处理余额
            // 2. ✅ 支持分期收款：通过多次调用实现分期收款
            // 3. 🔄 预收款抵扣：预留接口，待客户预收款模块完善
            // 4. ✅ 完善异常处理：收款失败时提供详细错误信息，不回滚应收单
            // 5. ✅ 状态同步：确保销售订单状态与出库、收款状态保持一致

            // 获取出库单信息，用于后续状态同步
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("出库单不存在");
            }

            Boolean receivableResult = generateReceivableAfterOutboundComplete(outboundId, operatorId, operatorName);
            if (!receivableResult) {
                throw new ServiceException("生成应收单失败");
            }

            // 获取生成的应收单ID
            FinArReceivableVo receivable = finArReceivableService.queryByOutboundId(outboundId);
            if (receivable == null) {
                throw new ServiceException("未找到生成的应收单");
            }
            Long receivableId = receivable.getReceivableId();
            BigDecimal receivableAmount = receivable.getAmount() != null ? receivable.getAmount() : BigDecimal.ZERO;

            // ✅ 支持部分收款：校验收款金额
            if (receiptAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("收款金额必须大于0");
            }

            if (receiptAmount.compareTo(receivableAmount) > 0) {
                throw new ServiceException(String.format("收款金额【%s】不能大于应收金额【%s】", receiptAmount, receivableAmount));
            }

            // TODO: [预收款抵扣功能] - 优先级: MEDIUM - 参考文档: docs/design/README_PREPAYMENT.md
            // 预收款抵扣逻辑：
            // 1. 查询客户预收款余额
            // 2. 如果有预收款，优先使用预收款抵扣
            // 3. 预收款不足时，剩余部分使用现金收款
            // BigDecimal prepaymentBalance = customerPrepaymentService.getBalance(outbound.getCustomerId());
            // if (prepaymentBalance.compareTo(BigDecimal.ZERO) > 0) {
            //     BigDecimal prepaymentUsed = prepaymentBalance.min(receiptAmount);
            //     customerPrepaymentService.useBalance(outbound.getCustomerId(), prepaymentUsed, "销售收款抵扣");
            //     receiptAmount = receiptAmount.subtract(prepaymentUsed);
            //     result.put("prepaymentUsed", prepaymentUsed);
            // }

            Long receiptId = null;
            Boolean applyResult = false;

            // 只有在还有现金收款时才生成收款单
            if (receiptAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 从应收单生成收款单
                receiptId = finArReceivableService.generateReceiptOrderFromReceivable(
                    receivableId, receiptAmount, accountId, operatorId, operatorName);

                if (receiptId == null) {
                    throw new ServiceException("生成收款单失败");
                }

                // 进行收款单与应收单核销
                applyResult = finArReceiptReceivableLinkService.applyReceiptToReceivable(
                    receiptId, receivableId, receiptAmount, "销售业务完整流程自动核销");

                if (!applyResult) {
                    throw new ServiceException("收款单与应收单核销失败");
                }
            }

            // 生成账户收入流水（只有现金收款时才生成）
            Boolean ledgerResult = true;
            if (receiptId != null) {
                ledgerResult = finAccountLedgerService.generateIncomeFromReceiptOrder(
                    receiptId, accountId, "销售业务完整流程收入");

                if (!ledgerResult) {
                    throw new ServiceException("生成账户收入流水失败");
                }
            }

            // ✅ 状态同步：更新销售订单状态
            updateSaleOrderStatusAfterOutbound(outbound, receiptAmount, receivableAmount);

            // 返回完整的业务结果
            result.put("success", true);
            result.put("outboundId", outboundId);
            result.put("outboundCode", outbound.getOutboundCode());
            result.put("receivableId", receivableId);
            result.put("receivableAmount", receivableAmount);
            result.put("receiptId", receiptId);
            result.put("receiptAmount", receiptAmount);
            result.put("accountId", accountId);
            result.put("isPartialPayment", receiptAmount.compareTo(receivableAmount) < 0);
            result.put("remainingAmount", receivableAmount.subtract(receiptAmount));
            result.put("message", receiptAmount.compareTo(receivableAmount) < 0 ?
                "销售业务完整流程执行成功（部分收款）" : "销售业务完整流程执行成功（全额收款）");

            log.info("销售业务完整流程执行成功 - 出库单: {}, 应收单: {}, 收款单: {}, 应收金额: {}, 收款金额: {}, 剩余金额: {}",
                outbound.getOutboundCode(), receivableId, receiptId, receivableAmount, receiptAmount,
                receivableAmount.subtract(receiptAmount));

            return result;
        } catch (Exception e) {
            log.error("销售业务完整流程执行失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);

            result.put("success", false);
            result.put("outboundId", outboundId);
            result.put("error", e.getMessage());
            result.put("message", "销售业务完整流程执行失败");

            throw new ServiceException("销售业务完整流程执行失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(SaleOutboundBo bo) {
        // 填充销售订单信息
//        if (bo.getOrderId() != null) {
//            SaleOrderVo saleOrder = saleOrderService.queryById(bo.getOrderId());
//            if (saleOrder != null) {
//                bo.setOrderCode(saleOrder.getOrderCode());
//                bo.setCustomerId(saleOrder.getCustomerId());
//                bo.setCustomerName(saleOrder.getCustomerName());
//            }
//        }

        // 填充客户信息
//        if (bo.getCustomerId() != null && StringUtils.isEmpty(bo.getCustomerName())) {
//            CompanyVo customer = companyService.queryById(bo.getCustomerId());
//            if (customer != null) {
//                bo.setCustomerName(customer.getCompanyName());
//            }
//        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(SaleOutboundBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        //TODO 需完善 如果是新增，设置申请人
//        if (bo.getOutboundId() == null) {
//            bo.setApplicantId(currentUserId);
//            bo.setApplicantName(currentUserName);
//        }

        // 设置经办人（每次更新都更新）
        bo.setHandlerId(currentUserId);
        bo.setHandlerName(currentUserName);
    }

    /**
     * 填充明细数据
     */
    private void fillItemsData(List<SaleOutboundItemBo> items, Long outboundId) {
        for (SaleOutboundItemBo item : items) {
            item.setOutboundId(outboundId);

            // 填充产品信息
            if (item.getProductId() != null) {
                ProductVo product = productService.queryById(item.getProductId());
                if (product != null) {
                    item.setProductCode(product.getProductCode());
                    item.setProductName(product.getProductName());
                    // TODO: SaleOutboundItemBo缺少productSpec字段，暂时注释
                    // TODO: 在SaleOutboundItemBo中添加setProductSpecs()方法
                    // item.setProductSpecs(product.getProductSpecs());
                    item.setUnitId(product.getUnitId());
                    item.setUnitName(product.getUnitName());

                    // 如果没有设置价格，使用产品的销售价格
                    if (item.getPrice() == null && product.getSalePrice() != null) {
                        item.setPrice(new BigDecimal(product.getSalePrice().toString()));
                    }
                }
            }
        }
    }

    /**
     * 计算明细金额（价税分离）
     */
    private void calculateItemAmounts(List<SaleOutboundItemBo> items) {
        for (SaleOutboundItemBo item : items) {
            if (item.getQuantity() != null && item.getPrice() != null) {
                BigDecimal quantity = item.getQuantity();
                BigDecimal price = item.getPrice(); // 含税价
                BigDecimal taxRate = item.getTaxRate() != null ? item.getTaxRate() : BigDecimal.ZERO;

                // 计算含税金额
                BigDecimal amount = price.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                // ✅ 优化：虽然BO类缺少amount字段，但可以通过Entity层更新
                // 将计算结果存储到临时变量，在保存时通过Entity设置
                // TODO: 建议为SaleOutboundItemBo添加amount字段以完善数据传递

                // 计算不含税价格和金额
                if (taxRate.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
                    BigDecimal priceExclusiveTax = price.divide(divisor, 4, RoundingMode.HALF_UP);
                    BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal taxAmount = amount.subtract(amountExclusiveTax);

                    item.setPriceExclusiveTax(priceExclusiveTax);
                    item.setAmountExclusiveTax(amountExclusiveTax);
                    item.setTaxAmount(taxAmount);
                } else {
                    // 无税情况
                    item.setPriceExclusiveTax(price);
                    item.setAmountExclusiveTax(amount);
                    item.setTaxAmount(BigDecimal.ZERO);
                }
            }
        }
    }

    /**
     * 更新主表汇总金额
     */
    private void updateTotalAmounts(Long outboundId) {
        try {
            if (outboundId == null) {
                log.warn("出库单ID为空，跳过金额合计更新");
                return;
            }

            // ✅ 使用标准的金额合计方法，符合DDD原则：聚合根Service调用子实体Mapper
            TaxCalculationResultBo totalAmount = itemMapper.calculateTotalAmount(outboundId);

            if (totalAmount == null) {
                log.debug("销售出库单【{}】没有明细，清空金额字段", outboundId);
                totalAmount = TaxCalculationResultBo.builder()
                    .amount(BigDecimal.ZERO)
                    .amountExclusiveTax(BigDecimal.ZERO)
                    .taxAmount(BigDecimal.ZERO)
                    .build();
            }

            // 更新主表金额字段
            SaleOutbound update = new SaleOutbound();
            update.setOutboundId(outboundId);
            update.setAmount(totalAmount.getAmount());
            update.setAmountExclusiveTax(totalAmount.getAmountExclusiveTax());
            update.setTaxAmount(totalAmount.getTaxAmount());

            // 执行主表更新
            int result = baseMapper.updateById(update);
            if (result > 0) {
                log.debug("更新销售出库单金额合计成功 - 出库单ID: {}, 含税金额: {}, 不含税金额: {}, 税额: {}",
                    outboundId, totalAmount.getAmount(), totalAmount.getAmountExclusiveTax(), totalAmount.getTaxAmount());
            } else {
                log.warn("更新销售出库单金额合计失败 - 出库单ID: {}", outboundId);
            }
        } catch (Exception e) {
            log.error("更新销售出库单金额合计异常 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据WMS状态更新出库单状态
     *
     * @param outboundId 销售出库单ID
     * @param wmsOutboundCode WMS出库单编码
     * @param actualQuantities 实际出库数量信息
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusByWms(Long outboundId, String wmsOutboundCode, Map<Long, BigDecimal> actualQuantities) {
        try {
            log.info("开始根据WMS状态更新销售出库单 - 出库单ID: {}, WMS编码: {}", outboundId, wmsOutboundCode);

            // 查询出库单和明细
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("销售出库单不存在，ID: " + outboundId);
            }

            // 校验当前状态是否允许WMS回传
            if (outbound.getOutboundStatus() != SaleOutboundStatus.PENDING_WAREHOUSE) {
                log.warn("销售出库单【{}】当前状态【{}】不允许WMS状态回传",
                    outbound.getOutboundCode(), outbound.getOutboundStatus());
                return false;
            }

            // ✅ 更新明细的实际出库数量
            if (actualQuantities != null && !actualQuantities.isEmpty()) {
                updateActualQuantities(outboundId, actualQuantities);
            }

            // ✅ 更新出库单状态为已完成
            SaleOutbound update = new SaleOutbound();
            update.setOutboundId(outboundId);
            update.setOutboundStatus(SaleOutboundStatus.COMPLETED);
            update.setOutboundTime(LocalDateTime.now());

            // 添加WMS回传信息到备注
            String wmsRemark = String.format(" [WMS出库完成 - WMS编码: %s, 回传时间: %s]",
                wmsOutboundCode, LocalDateTime.now());
            String newRemark = (outbound.getRemark() != null ? outbound.getRemark() : "") + wmsRemark;
            update.setRemark(newRemark);

            int result = baseMapper.updateById(update);

            if (result > 0) {
                // ✅ WMS出库完成后，自动触发应收单生成
                triggerReceivableGeneration(outbound);

                // ✅ 更新销售订单状态
                updateSaleOrderStatusAfterWmsCompletion(outbound);

                log.info("销售出库单WMS状态更新成功 - 出库单: {}, WMS编码: {}",
                    outbound.getOutboundCode(), wmsOutboundCode);
            }

            return result > 0;

        } catch (Exception e) {
            log.error("根据WMS状态更新销售出库单失败 - 出库单ID: {}, WMS编码: {}, 错误: {}",
                outboundId, wmsOutboundCode, e.getMessage(), e);
            throw new ServiceException("根据WMS状态更新销售出库单失败: " + e.getMessage());
        }
    }

    /**
     * 处理WMS异常
     *
     * @param outboundId 销售出库单ID
     * @param exceptionReason 异常原因
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleWmsException(Long outboundId, String exceptionReason) {
        try {
            log.info("开始处理销售出库单WMS异常 - 出库单ID: {}, 异常原因: {}", outboundId, exceptionReason);

            // 查询出库单
            SaleOutbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("销售出库单不存在，ID: " + outboundId);
            }

            // 更新状态为异常
            SaleOutbound update = new SaleOutbound();
            update.setOutboundId(outboundId);
            // TODO: 需要在 SaleOutboundStatus 枚举中添加 EXCEPTION 状态
            // update.setOutboundStatus(SaleOutboundStatus.EXCEPTION);
            update.setRemark(StringUtils.isNotBlank(outbound.getRemark())
                ? outbound.getRemark() + "; WMS异常: " + exceptionReason
                : "WMS异常: " + exceptionReason);

            int result = baseMapper.updateById(update);

            log.info("销售出库单WMS异常处理完成 - 出库单ID: {}, 更新结果: {}", outboundId, result > 0 ? "成功" : "失败");
            return result > 0;

        } catch (Exception e) {
            log.error("处理销售出库单WMS异常失败 - 出库单ID: {}, 异常原因: {}, 错误: {}",
                outboundId, exceptionReason, e.getMessage(), e);
            throw new ServiceException("处理销售出库单WMS异常失败: " + e.getMessage());
        }
    }

    // ================================ 状态同步辅助方法 ================================

    /**
     * 出库后更新销售订单状态
     * ✅ 实现销售订单与出库单状态的双向同步
     *
     * @param outbound 出库单信息
     * @param receiptAmount 收款金额
     * @param receivableAmount 应收金额
     */
    private void updateSaleOrderStatusAfterOutbound(SaleOutbound outbound, BigDecimal receiptAmount, BigDecimal receivableAmount) {
        try {
            if (outbound.getDirectSourceId() == null) {
                log.debug("出库单【{}】没有关联销售订单，跳过状态同步", outbound.getOutboundCode());
                return;
            }

            // TODO: [销售订单状态同步] - 优先级: HIGH - 参考文档: docs/design/README_STATE.md
            // 需要根据出库和收款情况更新销售订单状态：
            // 1. 出库完成 + 全额收款 → 订单状态更新为 CLOSED
            // 2. 出库完成 + 部分收款 → 订单状态更新为 PARTIALLY_PAID（需要新增状态）
            // 3. 出库完成 + 未收款 → 订单状态更新为 FULLY_SHIPPED
            // 4. 考虑一个订单对应多个出库单的情况

            Long saleOrderId = outbound.getDirectSourceId();

            // 判断收款状态
            boolean isFullyPaid = receiptAmount.compareTo(receivableAmount) >= 0;
            boolean isPartiallyPaid = receiptAmount.compareTo(BigDecimal.ZERO) > 0 && receiptAmount.compareTo(receivableAmount) < 0;

            // 根据收款情况确定目标状态
            SaleOrderStatus targetStatus;
            if (isFullyPaid) {
                targetStatus = SaleOrderStatus.CLOSED; // 全额收款，订单关闭
            } else if (isPartiallyPaid) {
                targetStatus = SaleOrderStatus.FULLY_SHIPPED; // 部分收款，保持已发货状态
            } else {
                targetStatus = SaleOrderStatus.FULLY_SHIPPED; // 未收款，已发货状态
            }

            // 调用销售订单服务更新状态
            // saleOrderService.updateOrderStatus(saleOrderId, targetStatus,
            //     String.format("出库单【%s】完成，收款金额：%s", outbound.getOutboundCode(), receiptAmount));

            log.info("销售订单状态同步完成 - 订单ID: {}, 目标状态: {}, 收款状态: {}",
                saleOrderId, targetStatus, isFullyPaid ? "全额收款" : (isPartiallyPaid ? "部分收款" : "未收款"));

        } catch (Exception e) {
            log.warn("更新销售订单状态失败 - 出库单: {}, 错误: {}", outbound.getOutboundCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新明细的实际出库数量
     *
     * @param outboundId       出库单ID
     * @param actualQuantities 实际出库数量映射（明细ID -> 实际数量）
     */
    private void updateActualQuantities(Long outboundId, Map<Long, BigDecimal> actualQuantities) {
        try {
            for (Map.Entry<Long, BigDecimal> entry : actualQuantities.entrySet()) {
                Long itemId = entry.getKey();
                BigDecimal actualQuantity = entry.getValue();

                // 更新明细的实际出库数量
                SaleOutboundItem updateItem = new SaleOutboundItem();
                updateItem.setItemId(itemId);
                updateItem.setActualQuantity(actualQuantity);

                int result = saleOutboundItemMapper.updateById(updateItem);
                if (result <= 0) {
                    log.warn("更新出库明细实际数量失败 - 明细ID: {}, 实际数量: {}", itemId, actualQuantity);
                } else {
                    log.debug("更新出库明细实际数量成功 - 明细ID: {}, 实际数量: {}", itemId, actualQuantity);
                }
            }
        } catch (Exception e) {
            log.error("更新明细实际出库数量失败 - 出库单ID: {}, 错误: {}", outboundId, e.getMessage(), e);
            throw new ServiceException("更新明细实际出库数量失败：" + e.getMessage());
        }
    }

    /**
     * WMS出库完成后，自动触发应收单生成
     *
     * @param outbound 出库单信息
     */
    private void triggerReceivableGeneration(SaleOutbound outbound) {
        try {
            // TODO: [应收单生成] - 优先级: HIGH - 参考文档: docs/design/README_FINANCE.md
            // 从销售出库单生成应收单的逻辑：
            // 1. 检查销售订单是否已生成应收单，避免重复生成
            // 2. 根据出库明细计算应收金额（含税/不含税）
            // 3. 创建应收单主记录，关联销售订单和出库单
            // 4. 创建应收单明细，关联出库明细
            // 5. 更新销售订单的应收状态

            log.info("触发应收单生成 - 出库单: {}, 销售订单: {}",
                outbound.getOutboundCode(), outbound.getSourceCode());

            // 暂时记录日志，待后续实现完整逻辑
            log.debug("应收单生成功能待实现 - 出库单ID: {}", outbound.getOutboundId());

        } catch (Exception e) {
            log.error("触发应收单生成失败 - 出库单: {}, 错误: {}",
                outbound.getOutboundCode(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * WMS出库完成后，更新销售订单状态
     *
     * @param outbound 出库单信息
     */
    private void updateSaleOrderStatusAfterWmsCompletion(SaleOutbound outbound) {
        try {
            // TODO: [销售订单状态更新] - 优先级: HIGH - 参考文档: docs/design/README_FLOW.md
            // WMS出库完成后更新销售订单状态的逻辑：
            // 1. 检查销售订单的所有出库单是否都已完成
            // 2. 如果全部完成，更新销售订单状态为"已出库"
            // 3. 如果部分完成，更新销售订单状态为"部分出库"
            // 4. 更新销售订单的实际出库数量统计

            log.info("更新销售订单状态 - 出库单: {}, 销售订单: {}",
                outbound.getOutboundCode(), outbound.getSourceCode());

            // 暂时记录日志，待后续实现完整逻辑
            log.debug("销售订单状态更新功能待实现 - 销售订单ID: {}", outbound.getSourceId());

        } catch (Exception e) {
            log.error("更新销售订单状态失败 - 出库单: {}, 错误: {}",
                outbound.getOutboundCode(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

}
