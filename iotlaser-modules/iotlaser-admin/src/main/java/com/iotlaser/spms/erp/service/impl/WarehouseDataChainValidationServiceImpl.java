package com.iotlaser.spms.erp.service.impl;

import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 仓储管理数据链路验证服务实现
 * 专门用于验证仓储管理模块与财务系统的数据传递完整性和一致性
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarehouseDataChainValidationServiceImpl implements IWarehouseDataChainValidationService {

    private final ISaleOrderService saleOrderService;
    private final IPurchaseOrderService purchaseOrderService;
    private final IInboundService inboundService;
    private final IInboundItemService inboundItemService;
    private final IOutboundService outboundService;
    private final IOutboundItemService outboundItemService;
    private final ITransferService transferService;
    private final IInventoryService inventoryService;
    private final IPurchaseInboundService purchaseInboundService;
    private final ISaleOutboundService saleOutboundService;

    /**
     * 验证采购订单→采购入库→仓库入库的数据链路
     *
     * @param purchaseOrderId 采购订单ID
     * @return 验证结果
     */
    @Override
    public IDataChainValidationService.DataChainValidationResult validatePurchaseInboundChain(Long purchaseOrderId) {
        try {
            log.info("开始验证采购入库数据链路 - 采购订单ID: {}", purchaseOrderId);

            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValidationType("PURCHASE_INBOUND_CHAIN");
            result.setTargetId(purchaseOrderId);
            result.setValidationTime(LocalDate.now());

            // 获取采购订单信息
            PurchaseOrderVo purchaseOrder = purchaseOrderService.queryById(purchaseOrderId);
            if (purchaseOrder == null) {
                result.setValid(false);
                result.addError("采购订单不存在: " + purchaseOrderId);
                return result;
            }

            // 获取采购入库单
            List<PurchaseInboundVo> purchaseInbounds = purchaseInboundService.selectListByPurchaseOrderId(purchaseOrderId);

            // 获取仓库入库单
            List<InboundItemVo> warehouseInbounds = inboundService.queryItemByDirectSourceId(purchaseOrderId, DirectSourceType.PURCHASE_INBOUND);

            // 验证数据链路完整性
            validatePurchaseInboundDataConsistency(purchaseOrder, purchaseInbounds, warehouseInbounds, result);

            // 验证库存创建
            validateInventoryCreation(warehouseInbounds, result);

            result.addDetail("采购订单编号", purchaseOrder.getOrderCode());
            result.addDetail("采购入库单数", String.valueOf(purchaseInbounds.size()));
            result.addDetail("仓库入库单数", String.valueOf(warehouseInbounds.size()));

            result.setValid(result.getErrors().isEmpty());

            log.info("采购入库数据链路验证完成 - 采购订单: {}, 结果: {}",
                purchaseOrder.getOrderCode(), result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证采购入库数据链路失败 - 采购订单ID: {}, 错误: {}", purchaseOrderId, e.getMessage(), e);
            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证销售订单→销售出库→仓库出库的数据链路
     *
     * @param saleOrderId 销售订单ID
     * @return 验证结果
     */
    @Override
    public IDataChainValidationService.DataChainValidationResult validateSaleOutboundChain(Long saleOrderId) {
        try {
            log.info("开始验证销售出库数据链路 - 销售订单ID: {}", saleOrderId);

            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValidationType("SALE_OUTBOUND_CHAIN");
            result.setTargetId(saleOrderId);
            result.setValidationTime(LocalDate.now());

            // 获取销售订单信息
            SaleOrderVo saleOrder = saleOrderService.queryById(saleOrderId);
            if (saleOrder == null) {
                result.setValid(false);
                result.addError("销售订单不存在: " + saleOrderId);
                return result;
            }

            // 获取销售出库单
            List<SaleOutboundVo> saleOutbounds = saleOutboundService.queryByOrderId(saleOrderId);

            // 获取仓库出库单
            List<OutboundItemVo> warehouseOutbounds = outboundService.queryItemByDirectSourceId(saleOrderId, DirectSourceType.SALE_OUTBOUND);

            // 验证数据链路完整性
            validateSaleOutboundDataConsistency(saleOrder, saleOutbounds, warehouseOutbounds, result);

            // 验证库存扣减
            validateInventoryDeduction(warehouseOutbounds, result);

            result.addDetail("销售订单编号", saleOrder.getOrderCode());
            result.addDetail("销售出库单数", String.valueOf(saleOutbounds.size()));
            result.addDetail("仓库出库单数", String.valueOf(warehouseOutbounds.size()));

            result.setValid(result.getErrors().isEmpty());

            log.info("销售出库数据链路验证完成 - 销售订单: {}, 结果: {}",
                saleOrder.getOrderCode(), result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证销售出库数据链路失败 - 销售订单ID: {}, 错误: {}", saleOrderId, e.getMessage(), e);
            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证库存移库操作的数据一致性
     *
     * @param transferId 移库单ID
     * @return 验证结果
     */
    @Override
    public IDataChainValidationService.DataChainValidationResult validateTransferConsistency(Long transferId) {
        try {
            log.info("开始验证库存移库一致性 - 移库单ID: {}", transferId);

            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValidationType("TRANSFER_CONSISTENCY");
            result.setTargetId(transferId);
            result.setValidationTime(LocalDate.now());

            // 获取移库单信息
            TransferVo transfer = transferService.queryById(transferId);
            if (transfer == null) {
                result.setValid(false);
                result.addError("移库单不存在: " + transferId);
                return result;
            }

            // 验证移库前后库存的一致性
            validateTransferBatchConsistency(transfer, result);

            // 验证移库数量和金额的平衡
            validateTransferQuantityBalance(transfer, result);

            result.addDetail("移库单编号", transfer.getTransferCode());
            result.addDetail("移库类型", transfer.getTransferType());
            result.addDetail("移库状态", transfer.getTransferStatus().toString());

            result.setValid(result.getErrors().isEmpty());

            log.info("库存移库一致性验证完成 - 移库单: {}, 结果: {}",
                transfer.getTransferCode(), result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证库存移库一致性失败 - 移库单ID: {}, 错误: {}", transferId, e.getMessage(), e);
            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证库存管理的完整性
     *
     * @param productId  产品ID
     * @param locationId 库位ID
     * @return 验证结果
     */
    @Override
    public IDataChainValidationService.DataChainValidationResult validateInventoryIntegrity(Long productId, Long locationId) {
        try {
            log.info("开始验证库存完整性 - 产品ID: {}, 库位ID: {}", productId, locationId);

            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValidationType("INVENTORY_BATCH_INTEGRITY");
            result.setTargetId(productId);
            result.setValidationTime(LocalDate.now());

            // 获取指定产品和库位的所有批次
            // TODO: 添加queryByProductAndLocation方法
            // List<InventoryVo> batches = inventoryService.queryByProductAndLocation(productId, locationId);

            // 验证批次状态的合理性
            // validateBatchStatusConsistency(batches, result);

            // 验证批次数量的准确性
            // validateBatchQuantityAccuracy(batches, result);

            // 验证批次有效期管理
            // validateBatchExpiryManagement(batches, result);

            // 验证批次成本核算
            // validateBatchCostAccounting(batches, result);

            result.addDetail("产品ID", productId.toString());
            result.addDetail("库位ID", locationId != null ? locationId.toString() : "全部库位");
            // result.addDetail("批次总数", String.valueOf(batches.size()));
            result.addDetail("批次总数", "0"); // TODO: 临时值

            result.setValid(result.getErrors().isEmpty());

            log.info("库存完整性验证完成 - 产品ID: {}, 结果: {}", productId, result.isValid());

            return result;

        } catch (Exception e) {
            log.error("验证库存完整性失败 - 产品ID: {}, 错误: {}", productId, e.getMessage(), e);
            IDataChainValidationService.DataChainValidationResult result = new IDataChainValidationService.DataChainValidationResult();
            result.setValid(false);
            result.addError("验证过程异常: " + e.getMessage());
            return result;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证采购入库数据一致性
     */
    private void validatePurchaseInboundDataConsistency(PurchaseOrderVo purchaseOrder,
                                                        List<PurchaseInboundVo> purchaseInbounds,
                                                        List<InboundItemVo> warehouseInbounds,
                                                        IDataChainValidationService.DataChainValidationResult result) {
        // 校验1: 采购订单状态是否正确
        if (purchaseOrder.getOrderStatus() == null ||
            (purchaseOrder.getOrderStatus() != com.iotlaser.spms.erp.enums.PurchaseOrderStatus.CONFIRMED &&
             purchaseOrder.getOrderStatus() != com.iotlaser.spms.erp.enums.PurchaseOrderStatus.PARTIALLY_RECEIVED &&
             purchaseOrder.getOrderStatus() != com.iotlaser.spms.erp.enums.PurchaseOrderStatus.FULLY_RECEIVED &&
             purchaseOrder.getOrderStatus() != com.iotlaser.spms.erp.enums.PurchaseOrderStatus.CLOSED)) {
            result.addError(String.format("采购订单 [%s] 状态为 %s，不是一个已确认或更高阶的状态。",
                purchaseOrder.getOrderCode(), purchaseOrder.getOrderStatus()));
        } else {
            // 只有在订单已确认的情况下，才校验下游单据
            // 校验2: 是否至少存在一个采购入库单
            if (purchaseInbounds == null || purchaseInbounds.isEmpty()) {
                result.addError(String.format("采购订单 [%s] 已确认，但未找到关联的采购入库单。", purchaseOrder.getOrderCode()));
            }
        }

        // TODO: 实现更深入的数据一致性验证
        // - 验证采购订单与采购入库单的数量、金额对应关系
        // - 验证采购入库单与仓库入库单的数据传递
        // - 验证供应商信息的一致性
        // - 验证产品信息的一致性

        result.addWarning("采购入库数据一致性验证功能待实现更深入的校验");
    }

    /**
     * 验证销售出库数据一致性
     */
    private void validateSaleOutboundDataConsistency(SaleOrderVo saleOrder,
                                                     List<SaleOutboundVo> saleOutbounds,
                                                     List<OutboundItemVo> warehouseOutbounds,
                                                     IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现销售出库数据一致性验证
        // 验证销售订单与销售出库单的数量、金额对应关系
        // 验证销售出库单与仓库出库单的数据传递
        // 验证客户信息的一致性
        // 验证产品信息的一致性

        result.addWarning("销售出库数据一致性验证功能待实现");
    }

    /**
     * 验证库存创建
     */
    private void validateInventoryCreation(List<InboundItemVo> warehouseInbounds,
                                           IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现库存创建验证
        // 验证每个入库单是否正确创建了库存
        // 验证批次数量与入库数量的一致性
        // 验证批次状态的正确性

        result.addWarning("库存创建验证功能待实现");
    }

    /**
     * 验证库存扣减
     */
    private void validateInventoryDeduction(List<OutboundItemVo> warehouseOutbounds,
                                            IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现库存扣减验证
        // 验证每个出库单是否正确扣减了库存
        // 验证FIFO扣减逻辑的正确性
        // 验证批次状态更新的正确性

        result.addWarning("库存扣减验证功能待实现");
    }

    /**
     * 验证移库批次一致性
     */
    private void validateTransferBatchConsistency(TransferVo transfer,
                                                  IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现移库批次一致性验证
        // 验证移出库位的批次扣减
        // 验证移入库位的批次增加
        // 验证移库前后总量不变

        result.addWarning("移库批次一致性验证功能待实现");
    }

    /**
     * 验证移库数量平衡
     */
    private void validateTransferQuantityBalance(TransferVo transfer,
                                                 IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现移库数量平衡验证
        // 验证移出数量 = 移入数量
        // 验证移库明细的数量汇总
        // 验证移库状态的正确性

        result.addWarning("移库数量平衡验证功能待实现");
    }

    /**
     * 验证批次状态一致性
     */
    private void validateBatchStatusConsistency(List<InventoryVo> batches,
                                                IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现批次状态一致性验证
        // 验证批次状态的合理性
        // 验证过期批次的处理
        // 验证冻结批次的管理

        result.addWarning("批次状态一致性验证功能待实现");
    }

    /**
     * 验证批次数量准确性
     */
    private void validateBatchQuantityAccuracy(List<InventoryVo> batches,
                                               IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现批次数量准确性验证
        // 验证批次数量的准确性
        // 验证可用数量的计算
        // 验证冻结数量的管理

        result.addWarning("批次数量准确性验证功能待实现");
    }

    /**
     * 验证批次有效期管理
     */
    private void validateBatchExpiryManagement(List<InventoryVo> batches,
                                               IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现批次有效期管理验证
        // 验证有效期设置的合理性
        // 验证过期批次的自动处理
        // 验证临期预警机制

        result.addWarning("批次有效期管理验证功能待实现");
    }

    /**
     * 验证批次成本核算
     */
    private void validateBatchCostAccounting(List<InventoryVo> batches,
                                             IDataChainValidationService.DataChainValidationResult result) {
        // TODO: 实现批次成本核算验证
        // 验证成本单价的准确性
        // 验证成本核算方法的一致性
        // 验证成本结转的正确性

        result.addWarning("批次成本核算验证功能待实现");
    }
}
