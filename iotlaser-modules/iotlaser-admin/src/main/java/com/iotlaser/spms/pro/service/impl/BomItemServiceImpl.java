package com.iotlaser.spms.pro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.pro.domain.BomItem;
import com.iotlaser.spms.pro.domain.bo.BomItemBo;
import com.iotlaser.spms.pro.domain.vo.BomItemVo;
import com.iotlaser.spms.pro.domain.vo.BomVo;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.mapper.BomItemMapper;
import com.iotlaser.spms.pro.service.IBomItemService;
import com.iotlaser.spms.pro.service.IBomService;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * BOM明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BomItemServiceImpl implements IBomItemService {

    private final BomItemMapper baseMapper;

    // TODO: [DDD重构] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，子实体Service不应该调用其他聚合根Service
    // 当前保留只读查询以维持功能完整性，后续需要重构为：
    // 1. 让调用方传入必要的产品信息，而不是在此处查询
    // 2. 或者将BomItem相关操作移到BomService中作为聚合根操作
    // 3. 使用领域事件来处理跨聚合的数据同步需求
    private final IProductService productService;  // TEMP: 仅用于只读查询产品信息
    private final IBomService bomService;          // TEMP: 仅用于只读查询BOM信息

    /**
     * 查询BOM明细
     *
     * @param itemId 主键
     * @return BOM明细
     */
    @Override
    public BomItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询BOM明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return BOM明细分页列表
     */
    @Override
    public TableDataInfo<BomItemVo> queryPageList(BomItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BomItem> lqw = buildQueryWrapper(bo);
        Page<BomItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的BOM明细列表
     *
     * @param bo 查询条件
     * @return BOM明细列表
     */
    @Override
    public List<BomItemVo> queryList(BomItemBo bo) {
        LambdaQueryWrapper<BomItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BomItem> buildQueryWrapper(BomItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BomItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BomItem::getItemId);
        lqw.eq(bo.getBomId() != null, BomItem::getBomId, bo.getBomId());
        lqw.eq(bo.getProductId() != null, BomItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), BomItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), BomItem::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSpecs()), BomItem::getProductSpecs, bo.getProductSpecs());
        lqw.eq(StringUtils.isNotBlank(bo.getProductType()), BomItem::getProductType, bo.getProductType());
        lqw.eq(bo.getCategoryId() != null, BomItem::getCategoryId, bo.getCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryCode()), BomItem::getCategoryCode, bo.getCategoryCode());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), BomItem::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getUnitId() != null, BomItem::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), BomItem::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), BomItem::getUnitName, bo.getUnitName());
        // ✅ 优化：移除数量的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, BomItem::getQuantity, bo.getQuantity());
        // TODO: 如需要可以后续添加数量范围查询支持
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BomItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增BOM明细
     *
     * @param bo BOM明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BomItemBo bo) {
        BomItem add = MapstructUtils.convert(bo, BomItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 批量新增更新BOM明细
     *
     * @param bos BOM明细
     * @return 是否新增成功
     */
    @Override
    public Boolean batchSave(List<BomItemBo> bos) {
        List<BomItem> batchs = new ArrayList<>();
        boolean flag = false;
        for (BomItemBo bo : bos) {
            BomItem entity = MapstructUtils.convert(bo, BomItem.class);
            if (ObjectUtil.isNull(bo.getItemId())) {
                validEntityBeforeSave(entity);
            } else {
                validEntityBeforeSave(entity);
            }
            batchs.add(entity);
        }
        if (!batchs.isEmpty()) {
            flag = baseMapper.insertOrUpdateBatch(batchs);
        }
        return flag;
    }

    /**
     * 修改BOM明细
     *
     * @param bo BOM明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BomItemBo bo) {
        BomItem update = MapstructUtils.convert(bo, BomItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BomItem entity) {
        // 校验必填字段
        if (entity.getBomId() == null) {
            throw new ServiceException("BOM不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("用量必须大于0");
        }

        // 校验同一BOM中产品不能重复
        if (entity.getBomId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<BomItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BomItem::getBomId, entity.getBomId());
            wrapper.eq(BomItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(BomItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一BOM中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除BOM明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验BOM明细是否可以删除
            List<BomItem> items = baseMapper.selectByIds(ids);
            for (BomItem item : items) {
                log.info("删除BOM明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量选择关联产品
     *
     * @param bomId      BOMID
     * @param productIds 需要关联的产品ID
     * @return 结果
     */
    @Override
    public int allocatedSelect(Long bomId, Long[] productIds) {
        int rows = 1;
        List<BomItem> list = new ArrayList<>();

        for (Long productId : productIds) {
            // ✅ 正确：通过Service接口获取产品信息
            ProductVo product = productService.queryById(productId);
            if (product != null) {
                BomItem bom = new BomItem();
                bom.setBomId(bomId);
                bom.setProductId(product.getProductId());
                bom.setProductCode(product.getProductCode());
                bom.setProductName(product.getProductName());
                bom.setProductType(product.getProductType());
                bom.setProductSpecs(product.getProductSpecs());
                bom.setUnitId(product.getUnitId());
                bom.setUnitCode(product.getUnitCode());
                bom.setUnitName(product.getUnitName());
                bom.setCategoryId(product.getCategoryId());
                bom.setCategoryCode(product.getCategoryCode());
                bom.setCategoryName(product.getCategoryName());
                list.add(bom);
            }
        }

        if (CollUtil.isNotEmpty(list)) {
            rows = baseMapper.insertBatch(list) ? list.size() : 0;
        }
        return rows;
    }

    /**
     * 根据条件分页查询未分配的产品列表
     *
     * @param bo 条件信息
     * @return 产品集合信息
     */
    @Override
    public TableDataInfo<BomItemVo> unallocatedList(BomItemBo bo, PageQuery pageQuery) {
        List<Long> productIds = baseMapper.selectProductIdsByBomId(bo.getBomId());

        // ✅ 正确：通过Service接口获取BOM信息
        BomVo bomVo = bomService.queryById(bo.getBomId());
        if (bomVo != null) {
            productIds.add(bomVo.getProductId());
        }

        String excludeProductIds = Convert.toStr(productIds);
        BomItemBo pageBo = new BomItemBo();
        pageBo.setExcludeProductIds(excludeProductIds);
        return queryPageListWith(bo, pageQuery);
    }


    /**
     * 查询BOM明细表及其关联信息
     *
     * @param itemId 主键
     * @return BOM明细表
     */
    @Override
    public BomItemVo queryByIdWith(Long itemId) {
        return MapstructUtils.convert(baseMapper.queryByIdWith(itemId), BomItemVo.class);
    }

    /**
     * 分页查询BOM明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return BOM明细表分页列表
     */
    @Override
    public TableDataInfo<BomItemVo> queryPageListWith(BomItemBo bo, PageQuery pageQuery) {
        QueryWrapper<BomItem> queryWrapper = buildQueryWrapperWith(bo);
        List<BomItemVo> result = MapstructUtils.convert(baseMapper.queryPageListWith(pageQuery.build(), queryWrapper), BomItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<BomItem> buildQueryWrapperWith(BomItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<BomItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), "item.product_id", StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        wrapper.eq(bo.getBomId() != null, "item.bom_id", bo.getBomId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductSpecs()), "item.product_specs", bo.getProductSpecs());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductType()), "item.product_type", bo.getProductType());
        wrapper.eq(bo.getCategoryId() != null, "item.category_id", bo.getCategoryId());
        wrapper.eq(StringUtils.isNotBlank(bo.getCategoryCode()), "item.category_code", bo.getCategoryCode());
        wrapper.like(StringUtils.isNotBlank(bo.getCategoryName()), "item.category_name", bo.getCategoryName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        // ✅ 优化：移除数量的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        // TODO: 如需要可以后续添加数量范围查询支持
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }


}
