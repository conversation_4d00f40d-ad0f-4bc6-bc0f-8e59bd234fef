package com.iotlaser.spms.pro.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.pro.domain.Bom;
import com.iotlaser.spms.pro.domain.BomItem;
import com.iotlaser.spms.pro.domain.bo.BomBo;
import com.iotlaser.spms.pro.domain.vo.BomVo;
import com.iotlaser.spms.pro.mapper.BomItemMapper;
import com.iotlaser.spms.pro.mapper.BomMapper;
import com.iotlaser.spms.pro.service.IBomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_BOM_CODE;

/**
 * BOMService业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BomServiceImpl implements IBomService {

    private final BomMapper baseMapper;
    private final BomItemMapper bomItemMapper;
    private final Gen gen;

    /**
     * 查询BOM
     *
     * @param bomId 主键
     * @return BOM
     */
    @Override
    public BomVo queryById(Long bomId) {
        return baseMapper.selectVoById(bomId);
    }

    /**
     * 分页查询BOM列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return BOM分页列表
     */
    @Override
    public TableDataInfo<BomVo> queryPageList(BomBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Bom> lqw = buildQueryWrapper(bo);
        Page<BomVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的BOM列表
     *
     * @param bo 查询条件
     * @return BOM列表
     */
    @Override
    public List<BomVo> queryList(BomBo bo) {
        LambdaQueryWrapper<Bom> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Bom> buildQueryWrapper(BomBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Bom> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Bom::getBomId);
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeBomIds()), Bom::getBomId, StringUtils.splitTo(bo.getExcludeBomIds(), Convert::toLong));
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), Bom::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        lqw.eq(StringUtils.isNotBlank(bo.getBomCode()), Bom::getBomCode, bo.getBomCode());
        lqw.like(StringUtils.isNotBlank(bo.getBomName()), Bom::getBomName, bo.getBomName());
        lqw.eq(bo.getProductId() != null, Bom::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), Bom::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Bom::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Bom::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增BOM
     *
     * @param bo BOM
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(BomBo bo) {
        if (StringUtils.isEmpty(bo.getBomCode())) {
            bo.setBomCode(gen.code(PRO_BOM_CODE));
        }
        Bom add = MapstructUtils.convert(bo, Bom.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBomId(add.getBomId());
            log.info("新增BOM成功，BOM编码：{}", add.getBomCode());
        }
        return flag;
    }

    /**
     * 修改BOM
     *
     * @param bo BOM
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(BomBo bo) {
        Bom update = MapstructUtils.convert(bo, Bom.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            log.info("修改BOM成功，BOM编码：{}", update.getBomCode());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Bom entity) {
        // 校验BOM编码唯一性
        if (StringUtils.isNotBlank(entity.getBomCode())) {
            LambdaQueryWrapper<Bom> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Bom::getBomCode, entity.getBomCode());
            if (entity.getBomId() != null) {
                wrapper.ne(Bom::getBomId, entity.getBomId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("BOM编码已存在：" + entity.getBomCode());
            }
        }


    }

    /**
     * 校验并批量删除BOM信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // ✅ DDD重构：聚合根Service直接管理子实体，检查BOM明细
            List<Bom> boms = baseMapper.selectByIds(ids);
            for (Bom bom : boms) {
                // 直接使用子实体Mapper查询，符合DDD聚合根管理子实体的原则
                LambdaQueryWrapper<BomItem> itemWrapper = Wrappers.lambdaQuery();
                itemWrapper.eq(BomItem::getBomId, bom.getBomId());
                List<BomItem> bomItems = bomItemMapper.selectList(itemWrapper);
                if (!bomItems.isEmpty()) {
                    throw new ServiceException("BOM【" + bom.getBomName() + "】存在明细配置，不能删除");
                }
                log.info("删除BOM校验：BOM编码【{}】BOM名称【{}】", bom.getBomCode(), bom.getBomName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除BOM成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除BOM失败：{}", e.getMessage(), e);
            throw new ServiceException("删除BOM失败：" + e.getMessage());
        }
    }
}
