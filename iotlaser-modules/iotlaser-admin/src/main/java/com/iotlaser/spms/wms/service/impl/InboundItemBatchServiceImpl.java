package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.wms.domain.InboundItemBatch;
import com.iotlaser.spms.wms.domain.bo.InboundItemBatchBo;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemBatchVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.InboundStatus;
import com.iotlaser.spms.wms.mapper.InboundItemBatchMapper;
import com.iotlaser.spms.wms.service.IInboundItemBatchService;
import com.iotlaser.spms.wms.service.IInboundItemService;
import com.iotlaser.spms.wms.service.IInboundService;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_BATCH_CODE;

/**
 * 产品入库批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InboundItemBatchServiceImpl implements IInboundItemBatchService {

    private final InboundItemBatchMapper baseMapper;
    private final Gen gen;

    // TODO: [DDD重构-子实体Service调用] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，子实体Service不应该调用其他聚合根Service或上级Service
    // 当前保留这些依赖以维持业务功能完整性，后续需要重构为：
    // 1. 将相关操作移到聚合根Service（InboundService）中
    // 2. 子实体Service只负责自身的CRUD操作
    // 3. 跨聚合的数据查询通过参数传入而不是在此处查询
    private final IInventoryService inventoryService;  // TEMP: 跨聚合调用
    private final ILocationService locationService;    // TEMP: 基础数据查询

    // TODO: [DDD违规] 子实体Service不应该调用聚合根Service或同级Service
    // 应该将相关逻辑移到InboundService中，或者通过参数传入必要数据
    @Lazy
    @Autowired
    private IInboundService inboundService;            // TEMP: 违反DDD原则，需要重构
    @Lazy
    @Autowired
    private IInboundItemService itemService;           // TEMP: 违反DDD原则，需要重构

    /**
     * 查询产品入库批次明细
     *
     * @param batchId 主键
     * @return 产品入库批次明细
     */
    @Override
    public InboundItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询产品入库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库批次明细分页列表
     */
    @Override
    public TableDataInfo<InboundItemBatchVo> queryPageList(InboundItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InboundItemBatch> lqw = buildQueryWrapper(bo);
        Page<InboundItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品入库批次明细列表
     *
     * @param bo 查询条件
     * @return 产品入库批次明细列表
     */
    @Override
    public List<InboundItemBatchVo> queryList(InboundItemBatchBo bo) {
        LambdaQueryWrapper<InboundItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InboundItemBatch> buildQueryWrapper(InboundItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InboundItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InboundItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, InboundItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getInboundId() != null, InboundItemBatch::getInboundId, bo.getInboundId());
        lqw.eq(bo.getInventoryId() != null, InboundItemBatch::getInventoryId, bo.getInventoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), InboundItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), InboundItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), InboundItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, InboundItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), InboundItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), InboundItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, InboundItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), InboundItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), InboundItemBatch::getUnitName, bo.getUnitName());


        lqw.eq(bo.getLocationId() != null, InboundItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), InboundItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), InboundItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InboundItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品入库批次明细
     *
     * @param bo 产品入库批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InboundItemBatchBo bo) {
        if (StringUtils.isEmpty(bo.getInternalBatchNumber())) {
            bo.setInternalBatchNumber(gen.code(PRO_BATCH_CODE));
        }
        //填充冗余信息
        fillRedundantFields(bo);
        InboundItemBatch add = MapstructUtils.convert(bo, InboundItemBatch.class);
        validEntityBeforeSave(add);
        itemService.updateFinishQuantityBeforeSave(add.getItemId(), add.getBatchId(), add.getQuantity());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改产品入库批次明细
     *
     * @param bo 产品入库批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InboundItemBatchBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        InboundItemBatch update = MapstructUtils.convert(bo, InboundItemBatch.class);
        validEntityBeforeSave(update);
        itemService.updateFinishQuantityBeforeSave(update.getItemId(), update.getBatchId(), update.getQuantity());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验（当前无特殊业务逻辑校验需求）
     *
     * @param entity 入库批次明细实体
     */
    private void validEntityBeforeSave(InboundItemBatch entity) {
        // 当前无特殊业务逻辑校验需求
        // 所有基础校验已移至Bo类注解实现
    }

    /**
     * 校验并批量删除产品入库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<InboundItemBatch> batches = baseMapper.selectByIds(ids);
            for (InboundItemBatch batch : batches) {
                // 检查主表状态，只有草稿状态的入库批次才能删除
                InboundVo inbound = inboundService.queryById(batch.getInboundId());
                if (inbound == null) {
                    throw new ServiceException("入库批次关联的入库单不存在，批次号：" + batch.getInternalBatchNumber());
                }
                if (inbound.getInboundStatus() != InboundStatus.PENDING_RECEIPT) {
                    throw new ServiceException("入库批次所属入库单【" + inbound.getInboundCode() +
                        "】状态为【" + inbound.getInboundStatus() + "】，不允许删除批次");
                }

                // 检查是否已关联库存记录
                // 通过内部批次号查询是否已生成库存
                if (StringUtils.isNotBlank(batch.getInternalBatchNumber())) {
                    InventoryBo queryBo = new InventoryBo();
                    queryBo.setInternalBatchNumber(batch.getInternalBatchNumber());
                    List<InventoryVo> inventoryes = inventoryService.queryList(queryBo);
                    if (!inventoryes.isEmpty()) {
                        throw new ServiceException("入库批次【" + batch.getInternalBatchNumber() +
                            "】已关联库存记录，不允许删除");
                    }
                    log.debug("入库批次【{}】库存关联检查通过", batch.getInternalBatchNumber());
                }

                log.info("删除入库批次明细校验通过，批次号：{}", batch.getInternalBatchNumber());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除入库批次成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除入库批次失败：{}", e.getMessage(), e);
            throw new ServiceException("删除入库批次失败：" + e.getMessage());
        }
    }

    /**
     * 批量新增产品入库批次明细
     *
     * @param batches 批次
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<InboundItemBatchBo> batches) {

        List<InboundItemBatch> entities = batches.stream()
            .map(bo -> MapstructUtils.convert(bo, InboundItemBatch.class))
            .collect(Collectors.toList());
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 根据明细ID查询
     *
     * @param itemId 明细ID
     * @return 库存记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<InboundItemBatchVo> queryByItemId(Long itemId) {
        LambdaQueryWrapper<InboundItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.eq(InboundItemBatch::getItemId, itemId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(InboundItemBatchBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }
}
