package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.enums.OutboundStatus;
import com.iotlaser.spms.wms.mapper.OutboundItemMapper;
import com.iotlaser.spms.wms.service.IOutboundItemService;
import com.iotlaser.spms.wms.service.IOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品出库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundItemServiceImpl implements IOutboundItemService {

    private final OutboundItemMapper baseMapper;

    // TODO: [DDD重构-子实体Service调用] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，子实体Service不应该调用聚合根Service
    // 当前保留这些依赖以维持业务功能完整性，后续需要重构为：
    // 1. 将相关操作移到聚合根Service（OutboundService）中
    // 2. 子实体Service只负责自身的CRUD操作
    private final ILocationService locationService;    // TEMP: 基础数据查询

    // TODO: [DDD违规] 子实体Service不应该调用聚合根Service
    // 应该将相关逻辑移到OutboundService中，或者通过参数传入必要数据
    @Lazy
    @Autowired
    private IOutboundService outboundService;          // TEMP: 违反DDD原则，需要重构

    /**
     * 查询产品出库明细
     *
     * @param itemId 主键
     * @return 产品出库明细
     */
    @Override
    public OutboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询产品出库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库明细分页列表
     */
    @Override
    public TableDataInfo<OutboundItemVo> queryPageList(OutboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OutboundItem> lqw = buildQueryWrapper(bo);
        Page<OutboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品出库明细列表
     *
     * @param bo 查询条件
     * @return 产品出库明细列表
     */
    @Override
    public List<OutboundItemVo> queryList(OutboundItemBo bo) {
        LambdaQueryWrapper<OutboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OutboundItem> buildQueryWrapper(OutboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OutboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OutboundItem::getItemId);
        lqw.eq(bo.getOutboundId() != null, OutboundItem::getOutboundId, bo.getOutboundId());
        lqw.eq(bo.getProductId() != null, OutboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OutboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), OutboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getLocationId() != null, OutboundItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), OutboundItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), OutboundItem::getLocationName, bo.getLocationName());

        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OutboundItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品出库明细
     *
     * @param bo 产品出库明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OutboundItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        OutboundItem add = MapstructUtils.convert(bo, OutboundItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改产品出库明细
     *
     * @param bo 产品出库明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OutboundItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        OutboundItem update = MapstructUtils.convert(bo, OutboundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验：
     * 同一出库单中产品不能重复
     *
     * @param entity 出库明细实体
     */
    private void validEntityBeforeSave(OutboundItem entity) {
        // 校验同一出库单中产品不能重复
        if (entity.getOutboundId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<OutboundItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(OutboundItem::getOutboundId, entity.getOutboundId());
            wrapper.eq(OutboundItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(OutboundItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一出库单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除产品出库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验出库明细是否可以删除
            List<OutboundItem> items = baseMapper.selectByIds(ids);
            for (OutboundItem item : items) {
                // 检查主表状态，只有草稿状态的出库明细才能删除
                OutboundVo outbound = outboundService.queryById(item.getOutboundId());
                if (outbound != null && !OutboundStatus.PENDING_PICKING.getValue().equals(outbound.getOutboundStatus())) {
                    throw new ServiceException("出库明细所属出库单【" + outbound.getOutboundCode() +
                        "】状态为【" + outbound.getOutboundStatus() + "】，不允许删除明细");
                }

                // 级联删除出库明细批次
                // TODO: 添加existsByItemId和getBatchIdsByItemId方法
                // if (batchService.existsByItemId(item.getItemId())) {
                //     List<Long> batchIds = batchService.getBatchIdsByItemId(item.getItemId());
                //     if (!batchIds.isEmpty()) {
                //         batchService.deleteWithValidByIds(batchIds, false);
                //         log.info("级联删除出库明细批次，明细：{}，批次数量：{}", item.getProductName(), batchIds.size());
                //     }
                // }

                log.info("删除出库明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除出库明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除出库明细失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入或更新产品出库明细表
     *
     * @param items 明细
     * @return 是否插入成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<OutboundItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<OutboundItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, OutboundItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售出库明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(OutboundItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }

}
