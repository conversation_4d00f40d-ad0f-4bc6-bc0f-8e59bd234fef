package com.iotlaser.spms.common.service.impl;

import com.iotlaser.spms.common.domain.bo.PriceCalculationResult;
import com.iotlaser.spms.common.service.IPriceCalculationService;
import org.dromara.common.core.exception.ServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PriceCalculationServiceImpl 单元测试
 * <p>
 * 测试重构后的价格计算服务，验证：
 * - 与 TaxCalculationUtils 的集成
 * - 税率格式转换的正确性
 * - 精度调整的兼容性
 * - 异常处理的完整性
 *
 * <AUTHOR> <PERSON>
 * @date 2025/07/11
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("价格计算服务实现类测试")
class PriceCalculationServiceImplTest {

    private IPriceCalculationService priceCalculationService;

    @BeforeEach
    void setUp() {
        priceCalculationService = new PriceCalculationServiceImpl();
    }

    /**
     * 测试基于含税单价的完整计算
     */
    @Test
    @DisplayName("基于含税单价计算 - 正常场景")
    void testCalculateFromInclusivePrice() {
        // Given
        BigDecimal price = new BigDecimal("113.00");
        BigDecimal taxRate = new BigDecimal("0.13"); // 小数格式：13%
        BigDecimal quantity = new BigDecimal("10");

        // When
        PriceCalculationResult result = priceCalculationService.calculateFromInclusivePrice(
            price, taxRate, quantity);

        // Then
        assertNotNull(result);
        assertEquals(price, result.getPrice());
        assertEquals(taxRate, result.getTaxRate()); // 保持小数格式
        assertEquals(quantity, result.getQuantity());
        
        // 验证计算结果（注意：结果应该是4位小数精度）
        assertEquals(new BigDecimal("1130.0000"), result.getAmount()); // 113 * 10
        assertEquals(new BigDecimal("100.0000"), result.getPriceExclusiveTax()); // 113 / 1.13
        assertEquals(new BigDecimal("1000.0000"), result.getAmountExclusiveTax()); // 100 * 10
        assertEquals(new BigDecimal("130.0000"), result.getTaxAmount()); // 1130 - 1000
    }

    /**
     * 测试含税金额计算
     */
    @Test
    @DisplayName("含税金额计算 - 正常场景")
    void testCalculateAmount() {
        // Given
        BigDecimal quantity = new BigDecimal("5");
        BigDecimal price = new BigDecimal("100.00");

        // When
        BigDecimal result = priceCalculationService.calculateAmount(quantity, price);

        // Then
        assertEquals(new BigDecimal("500.0000"), result);
    }

    /**
     * 测试不含税金额计算
     */
    @Test
    @DisplayName("不含税金额计算 - 正常场景")
    void testCalculateAmountExclusiveTax() {
        // Given
        BigDecimal quantity = new BigDecimal("10");
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");

        // When
        BigDecimal result = priceCalculationService.calculateAmountExclusiveTax(quantity, priceExclusiveTax);

        // Then
        assertEquals(new BigDecimal("1000.0000"), result);
    }

    /**
     * 测试税额计算
     */
    @Test
    @DisplayName("税额计算 - 正常场景")
    void testCalculateTaxAmount() {
        // Given
        BigDecimal amountExclusiveTax = new BigDecimal("1000.00");
        BigDecimal taxRate = new BigDecimal("0.13"); // 小数格式：13%

        // When
        BigDecimal result = priceCalculationService.calculateTaxAmount(amountExclusiveTax, taxRate);

        // Then
        assertEquals(new BigDecimal("130.0000"), result);
    }

    /**
     * 测试不含税单价计算
     */
    @Test
    @DisplayName("不含税单价计算 - 正常场景")
    void testCalculatePriceExclusiveTax() {
        // Given
        BigDecimal price = new BigDecimal("113.00");
        BigDecimal taxRate = new BigDecimal("0.13"); // 小数格式：13%

        // When
        BigDecimal result = priceCalculationService.calculatePriceExclusiveTax(price, taxRate);

        // Then
        assertEquals(new BigDecimal("100.0000"), result);
    }

    /**
     * 测试含税单价计算
     */
    @Test
    @DisplayName("含税单价计算 - 正常场景")
    void testCalculatePrice() {
        // Given
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");
        BigDecimal taxRate = new BigDecimal("0.13"); // 小数格式：13%

        // When
        BigDecimal result = priceCalculationService.calculatePrice(priceExclusiveTax, taxRate);

        // Then
        assertEquals(new BigDecimal("113.0000"), result);
    }

    /**
     * 测试价格一致性校验 - 一致的情况
     */
    @Test
    @DisplayName("价格一致性校验 - 一致场景")
    void testValidatePriceConsistency_Consistent() {
        // Given - 使用一致的数据
        BigDecimal quantity = new BigDecimal("10");
        BigDecimal price = new BigDecimal("113.00");
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");
        BigDecimal amount = new BigDecimal("1130.00");
        BigDecimal amountExclusiveTax = new BigDecimal("1000.00");
        BigDecimal taxRate = new BigDecimal("0.13");
        BigDecimal taxAmount = new BigDecimal("130.00");

        // When
        boolean result = priceCalculationService.validatePriceConsistency(
            quantity, price, priceExclusiveTax, amount, amountExclusiveTax, taxRate, taxAmount);

        // Then
        assertTrue(result);
    }

    /**
     * 测试价格一致性校验 - 不一致的情况
     */
    @Test
    @DisplayName("价格一致性校验 - 不一致场景")
    void testValidatePriceConsistency_Inconsistent() {
        // Given - 使用不一致的数据
        BigDecimal quantity = new BigDecimal("10");
        BigDecimal price = new BigDecimal("113.00");
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");
        BigDecimal amount = new BigDecimal("1130.00");
        BigDecimal amountExclusiveTax = new BigDecimal("1000.00");
        BigDecimal taxRate = new BigDecimal("0.13");
        BigDecimal taxAmount = new BigDecimal("999.99"); // 错误的税额

        // When
        boolean result = priceCalculationService.validatePriceConsistency(
            quantity, price, priceExclusiveTax, amount, amountExclusiveTax, taxRate, taxAmount);

        // Then
        assertFalse(result);
    }

    /**
     * 测试null值处理
     */
    @Test
    @DisplayName("null值处理测试")
    void testNullValueHandling() {
        // 测试各个方法对null值的处理
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculateAmount(null, new BigDecimal("100")));
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculateAmount(new BigDecimal("10"), null));
        
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculateAmountExclusiveTax(null, new BigDecimal("100")));
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculateAmountExclusiveTax(new BigDecimal("10"), null));
        
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculateTaxAmount(null, new BigDecimal("0.13")));
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculateTaxAmount(new BigDecimal("1000"), null));
        
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculatePriceExclusiveTax(null, new BigDecimal("0.13")));
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculatePriceExclusiveTax(new BigDecimal("113"), null));
        
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculatePrice(null, new BigDecimal("0.13")));
        assertEquals(BigDecimal.ZERO, priceCalculationService.calculatePrice(new BigDecimal("100"), null));
    }

    /**
     * 测试异常场景
     */
    @Test
    @DisplayName("异常场景测试")
    void testExceptionScenarios() {
        // 测试无效的税率（超出范围）
        assertThrows(ServiceException.class, () -> {
            priceCalculationService.calculateFromInclusivePrice(
                new BigDecimal("100"), new BigDecimal("1.5"), new BigDecimal("1")); // 150%税率
        });

        // 测试无效的数量
        assertThrows(ServiceException.class, () -> {
            priceCalculationService.calculateFromInclusivePrice(
                new BigDecimal("100"), new BigDecimal("0.13"), BigDecimal.ZERO); // 数量为0
        });
    }

    /**
     * 测试精度兼容性
     */
    @Test
    @DisplayName("精度兼容性测试")
    void testPrecisionCompatibility() {
        // Given
        BigDecimal price = new BigDecimal("113.00");
        BigDecimal taxRate = new BigDecimal("0.13");
        BigDecimal quantity = new BigDecimal("3"); // 使用会产生无限小数的数量

        // When
        PriceCalculationResult result = priceCalculationService.calculateFromInclusivePrice(
            price, taxRate, quantity);

        // Then - 验证所有金额字段都有4位小数精度（保持兼容性）
        assertEquals(4, result.getAmount().scale());
        assertEquals(4, result.getAmountExclusiveTax().scale());
        assertEquals(4, result.getTaxAmount().scale());
        assertEquals(4, result.getPrice().scale());
        assertEquals(4, result.getPriceExclusiveTax().scale());
    }

    /**
     * 测试税率格式转换
     */
    @Test
    @DisplayName("税率格式转换测试")
    void testTaxRateFormatConversion() {
        // Given - 使用小数税率
        BigDecimal decimalTaxRate = new BigDecimal("0.13");
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");

        // When
        BigDecimal inclusivePrice = priceCalculationService.calculatePrice(priceExclusiveTax, decimalTaxRate);

        // Then - 验证转换正确
        assertEquals(new BigDecimal("113.0000"), inclusivePrice);

        // 反向验证
        BigDecimal calculatedExclusivePrice = priceCalculationService.calculatePriceExclusiveTax(
            inclusivePrice, decimalTaxRate);
        assertEquals(new BigDecimal("100.0000"), calculatedExclusivePrice);
    }
}
